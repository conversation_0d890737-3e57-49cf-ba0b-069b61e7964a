# 💧 做WaterTime插件的故事

## 🌱 **为什么要做这个插件**

说起来挺简单的，就是因为我老是忘记喝水。

那天下午我坐在电脑前写代码，一写就是四个小时。突然感觉头晕，这才发现桌上的水杯从早上倒满水后就没动过。我想，这也太不像话了吧，连喝水这么基本的事情都能忘记。

其实不只是我，身边很多朋友都有这个问题。大家工作一忙起来，什么都能想起来，就是忘记照顾自己。回微信、开会、赶项目，这些都记得清清楚楚，但是喝水？完全不在脑子里。

我就想，既然我们天天对着电脑，为什么不让电脑来提醒我们喝水呢？

## 💭 **开始的想法**

我先去网上找了找，确实有一些喝水提醒的软件，但是用起来都挺麻烦的。要么需要单独下载一个APP，要么功能特别复杂，设置一大堆东西。

我想要的很简单：就是在我上网的时候，能有个小东西提醒我喝水就行了。不要太复杂，不要太显眼，但是要有用。

既然我每天大部分时间都在用浏览器，那做个浏览器插件不是正好吗？就在网页上放个小圆圈，想喝水的时候点一下记录一下，简单明了。

## 🎨 **怎么设计的**

### **越简单越好**

我的想法很简单：这个插件一定要简单到不能再简单。

就一个小圆圈浮在网页上，点一下就记录喝了一次水。不要什么复杂的菜单，不要什么花里胡哨的功能，就这么简单。因为如果太复杂了，我自己都懒得用。

### **为什么选蓝色**

颜色的话，我选了蓝色。原因很直接：水是蓝色的嘛。而且蓝色看起来比较舒服，不会太刺眼，工作的时候看到也不会觉得烦。

界面做得透明一点，有点毛玻璃的感觉，这样看起来比较现代，也不会太突兀。毕竟是要放在网页上的，太显眼了会影响正常浏览。

## 🤝 **使用中发现的问题**

### **拖拽的时候老是误触**

做出来之后，我自己用了一段时间，发现了一个问题：我想拖动小圆圈换个位置的时候，经常会不小心触发记录，就是明明想移动位置，结果变成了记录喝水。

这挺烦人的，所以我就想了个办法：如果你按住圆圈的时间超过0.5秒，或者拖动的距离超过5个像素，那就认为你是想移动位置，不会记录喝水。这样就不会误触了。

### **日历的颜色设计**

后来我又加了个日历功能，可以看每天的喝水情况。我用不同的颜色来表示：
- 绿色：今天喝够了，棒！
- 黄色：喝了一些，但还不够
- 红色：今天喝得太少了，要注意

这样一眼就能看出自己最近的喝水情况怎么样。看到满屏的绿色会很有成就感，看到红色就知道要加油了。

## 🌟 **一些小细节**

### **撤回功能**

有时候会不小心点错，比如明明没喝水却点了记录。所以我加了个撤回功能，点错了可以马上撤销。

这个功能挺重要的，因为人总是会犯错嘛。如果点错了还不能改，那用起来就会很紧张，生怕点错。有了撤回功能，用起来就轻松多了。

### **为什么历史记录不能改**

有人问我，为什么只能撤回最近的一次记录，以前的记录为什么不能修改？

我是这么想的：过去的就过去了，没必要去改。如果什么都能改，那记录就没意义了。而且，接受过去的不完美，专注于今天做得更好，这样更健康一些。

## 💝 **一些小心思**

### **动画效果**

我给界面加了一些小动画，比如点击的时候会有涟漪效果，界面出现的时候会有淡入效果。这些不是为了炫技，就是想让使用起来更舒服一点。

### **提示文字**

写提示文字的时候，我尽量用比较友好的语气，比如"💡 浮动圆圈已显示在页面右下角，点击即可快速记录饮水"。不想写得像说明书那样冷冰冰的，希望用起来像朋友在提醒你一样。

### **制作者信息**

在插件里加了"由 @Renais 制作"这行字。不是为了炫耀，就是想让用的人知道这是我做的，如果有什么问题可以找我。

## 🌈 **用户的反馈**

### **听取建议，不断改进**

做出来之后，有朋友试用了给我反馈。有人说拖拽的时候容易误触，我就加了防误触的功能；有人说想看历史记录，我就做了日历；有人说界面可以再好看一点，我就加了毛玻璃效果。

每次收到反馈我都挺开心的，说明真的有人在用，而且用心在用。这些建议让插件变得越来越好用。

### **从工具到伙伴**

一开始我只是想做个简单的计数器，点一下记录一次喝水。但是慢慢地我发现，这个小插件好像变成了一个小伙伴，每天陪着大家工作，提醒大家照顾自己。

这种感觉挺好的，不只是一个冷冰冰的工具，而是真的在关心使用它的人。

## 🔮 **一些感想**

### **简单的力量**

做这个插件让我明白了一个道理：有时候最简单的东西反而最有用。不需要复杂的功能，不需要花哨的界面，就一个小圆圈，点一下记录一次，但是真的能帮到人。

### **技术的温度**

以前我觉得写代码就是写代码，功能实现了就行。但是做WaterTime让我发现，技术也可以有温度。每一个小细节，每一句提示文字，都可以传达关怀。

虽然只是个小插件，但是如果能让用它的人多喝点水，身体更健康一点，那就很有意义了。

## 💭 **做插件的体会**

### **好的产品应该是无感的**

做WaterTime让我明白，好的产品应该让人感觉不到它的存在。用户不需要知道我是怎么实现防误触的，不需要了解数据是怎么存储的，他们只要觉得好用就行了。

最好的技术就是让人感觉不到技术的存在。

### **简单不等于简陋**

WaterTime看起来很简单，就一个圆圈，几个按钮，一个日历。但是为了做到这种简单，背后其实花了很多心思。

怎么让交互更自然，怎么让界面更舒服，怎么防止误操作，这些都需要仔细考虑。真正的简单，是把复杂的东西藏起来，让用户用起来毫不费力。

## 🌸 **最想说的话**

### **每个人都应该被关怀**

现在生活节奏这么快，大家都忙着工作、学习、赚钱，经常忘记照顾自己。WaterTime想要传达的不只是"记得喝水"，更是"记得关心自己"。

每次点击记录，其实就是在关爱自己；每天坚持使用，就是在对自己的健康负责；看到日历上的绿色圆点，就是对自己努力的肯定。

### **小东西也能有大作用**

WaterTime就是个小插件，功能也很简单。但是如果能让用它的人养成喝水的好习惯，让大家的身体更健康一点，那就很有价值了。

有时候改变不需要什么宏大的计划，就是一个小小的提醒，一点点的关怀，就够了。

## 🎯 **最后想说的**

做WaterTime这个插件，让我明白了一个道理：有时候最简单的关怀，反而最有力量。

每一次点击都很简单，每一天的坚持都很平常，但是积累起来就能养成好习惯，让身体更健康。

我希望每个用WaterTime的人，都能感受到这份小小的关怀。希望在忙碌的工作中，在紧张的学习里，这个小工具能够提醒你：

**停下来，喝口水，照顾一下自己。**

因为身体健康比什么都重要。

---

**—— @Renais**
*2025年1月*
