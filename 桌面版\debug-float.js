// 浮窗调试脚本
// 在浮窗控制台中运行此脚本来诊断问题

console.log('=== 浮窗调试开始 ===');

// 1. 检查数据管理器
console.log('1. 检查数据管理器:');
console.log('window.dataManager:', window.dataManager);
console.log('dataManager.isInitialized:', window.dataManager?.isInitialized);

// 2. 检查浮窗实例
console.log('2. 检查浮窗实例:');
console.log('window.floatWidget:', window.floatWidget);
console.log('floatWidget.data:', window.floatWidget?.data);

// 3. 检查DOM元素
console.log('3. 检查DOM元素:');
const elements = {
    floatContainer: document.getElementById('floatContainer'),
    floatWidget: document.getElementById('floatWidget'),
    floatPercentage: document.getElementById('floatPercentage'),
    floatAmount: document.getElementById('floatAmount'),
    progressArc: document.getElementById('progressArc'),
    contextMenu: document.getElementById('contextMenu')
};

Object.entries(elements).forEach(([name, element]) => {
    console.log(`${name}:`, element ? '✓' : '✗');
});

// 4. 测试数据获取
async function testDataRetrieval() {
    console.log('4. 测试数据获取:');
    
    if (!window.dataManager) {
        console.error('数据管理器不存在');
        return;
    }

    try {
        // 获取设置
        const settings = await window.dataManager.getSettings();
        console.log('设置:', settings);

        // 获取今日数据
        const todayData = await window.dataManager.getTodayData();
        console.log('今日数据:', todayData);

        // 检查键名
        const todayKey = window.dataManager.getTodayKey();
        console.log('今日键名:', todayKey);

        // 直接从存储获取
        const rawData = await window.electronAPI.getStoreData(todayKey);
        console.log('原始存储数据:', rawData);

    } catch (error) {
        console.error('数据获取失败:', error);
    }
}

// 5. 测试点击事件
function testClickEvent() {
    console.log('5. 测试点击事件:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        console.log('模拟点击事件...');
        widget.click();
    } else {
        console.error('找不到浮窗元素');
    }
}

// 6. 测试右键菜单
function testContextMenu() {
    console.log('6. 测试右键菜单:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        console.log('模拟右键事件...');
        const event = new MouseEvent('contextmenu', {
            bubbles: true,
            cancelable: true,
            clientX: 60,
            clientY: 60
        });
        widget.dispatchEvent(event);
    } else {
        console.error('找不到浮窗元素');
    }
}

// 7. 强制刷新数据
async function forceRefreshData() {
    console.log('7. 强制刷新数据:');
    
    if (window.floatWidget) {
        try {
            await window.floatWidget.forceSyncData();
            console.log('数据刷新完成');
        } catch (error) {
            console.error('数据刷新失败:', error);
        }
    } else {
        console.error('浮窗实例不存在');
    }
}

// 执行测试
(async () => {
    await testDataRetrieval();
    
    // 提供手动测试函数
    window.debugFloat = {
        testClick: testClickEvent,
        testContextMenu: testContextMenu,
        forceRefresh: forceRefreshData,
        testData: testDataRetrieval
    };
    
    console.log('=== 调试函数已注册到 window.debugFloat ===');
    console.log('可用函数: testClick(), testContextMenu(), forceRefresh(), testData()');
})();
