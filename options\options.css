/* WaterTime 设置页面样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #E0F6FF 0%, #B0E0E6 50%, #87CEEB 100%);
    color: #2F4F4F;
    line-height: 1.6;
    min-height: 100vh;
}

/* 主容器 */
.container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 40px;
    margin-bottom: 40px;
    overflow: hidden;
}

/* 头部 */
.header {
    background: rgba(255, 255, 255, 0.2);
    padding: 30px 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 10px;
}

.logo h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2F4F4F;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.subtitle {
    font-size: 16px;
    color: #5F9EA0;
    opacity: 0.9;
}

/* 主要内容 */
.main-content {
    padding: 40px;
}

/* 设置区块 */
.settings-section {
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: 600;
    color: #2F4F4F;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(0, 191, 255, 0.2);
}

.section-title svg {
    color: #00BFFF;
}

/* 设置网格 */
.settings-grid {
    display: grid;
    gap: 25px;
}

/* 设置项 */
.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-label {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.label-text {
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.label-desc {
    font-size: 14px;
    color: #5F9EA0;
    opacity: 0.8;
}

/* 输入组 */
.input-group {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
}

.input-group:focus-within {
    border-color: #00BFFF;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

.setting-input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #2F4F4F;
    outline: none;
}

.setting-input::placeholder {
    color: #5F9EA0;
    opacity: 0.6;
}

.input-unit {
    padding: 12px 16px;
    background: rgba(0, 191, 255, 0.1);
    color: #00BFFF;
    font-weight: 500;
    font-size: 14px;
}

/* 选择框 */
.setting-select {
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.3);
    font-size: 16px;
    color: #2F4F4F;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-select:focus {
    outline: none;
    border-color: #00BFFF;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

/* 切换开关 */
.toggle-switch {
    display: flex;
    align-items: center;
}

.toggle-input {
    display: none;
}

.toggle-label {
    position: relative;
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-input:checked + .toggle-label {
    background: #00BFFF;
    border-color: #00BFFF;
}

.toggle-input:checked + .toggle-label .toggle-slider {
    transform: translateX(30px);
}

/* 输入错误 */
.input-error {
    font-size: 14px;
    color: #FF6B6B;
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.input-error.show {
    opacity: 1;
}

/* 数据操作 */
.data-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #5F9EA0;
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn.danger {
    background: rgba(255, 107, 107, 0.2);
    color: #FF6B6B;
    border-color: rgba(255, 107, 107, 0.3);
}

.action-btn.danger:hover {
    background: rgba(255, 107, 107, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
}

/* 底部 */
.footer {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.primary-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn.primary {
    background: #00BFFF;
    color: white;
    border-color: #00BFFF;
}

.btn.primary:hover {
    background: #0099CC;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 191, 255, 0.4);
}

.btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #5F9EA0;
}

.btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

/* 保存状态 */
.save-status {
    text-align: center;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-status.show {
    opacity: 1;
}

.save-status.success {
    color: #32CD32;
}

.save-status.error {
    color: #FF6B6B;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 20px;
        border-radius: 16px;
    }
    
    .header,
    .main-content,
    .footer {
        padding: 20px;
    }
    
    .settings-section {
        padding: 20px;
    }
    
    .footer-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .primary-actions {
        justify-content: center;
    }
    
    .data-actions {
        justify-content: center;
    }
}

/* 动画效果 */
.settings-section {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
