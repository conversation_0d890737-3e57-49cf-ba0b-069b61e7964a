<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime - 饮水记录助手</title>
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-container">


        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 今日概览 -->
            <div class="today-overview">
                <div class="overview-card">
                    <div class="overview-main">
                        <div class="overview-label">今日进度</div>
                        <div class="overview-value">
                            <span class="percentage" id="todayPercentage">0%</span>
                            <span class="amount">
                                <span id="todayAmount">0ml</span> /
                                <span id="targetAmount">2000ml</span>
                            </span>
                        </div>
                    </div>
                    <div class="overview-remaining">
                        <span>还需 <span id="remainingAmount">2000ml</span></span>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="top-actions">
                <button id="quickDrink" class="action-btn primary">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z"/>
                    </svg>
                    <span>快速记录</span>
                    <span id="quickAmount">200ml</span>
                </button>
                <button id="quickUndo" class="action-btn secondary">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12.5,8C9.85,8 7.45,9 5.6,10.6L2,7V16H11L7.38,12.38C8.77,11.22 10.54,10.5 12.5,10.5C16.04,10.5 19.05,12.81 20.1,16L22.47,15.22C21.08,11.03 17.15,8 12.5,8Z"/>
                    </svg>
                    <span>快速撤回</span>
                </button>
            </div>

            <!-- 快速设置 -->
            <div class="quick-settings">
                <div class="setting-item">
                    <label for="dailyTarget">每日目标</label>
                    <div class="input-wrapper">
                        <input type="number" id="dailyTarget" min="500" max="5000" step="100" value="2000">
                        <span class="input-unit">ml</span>
                    </div>
                </div>
                <div class="setting-item">
                    <label for="singleAmount">单次饮水量</label>
                    <div class="input-wrapper">
                        <input type="number" id="singleAmount" min="50" max="1000" step="50" value="200">
                        <span class="input-unit">ml</span>
                    </div>
                </div>
            </div>

            <!-- 饮水日历 -->
            <div class="water-calendar">
                <div class="calendar-header">
                    <button id="prevMonth" class="nav-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                        </svg>
                    </button>
                    <h3 id="currentMonth">2025年7月</h3>
                    <button id="nextMonth" class="nav-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                        </svg>
                    </button>
                </div>
                <div class="calendar-weekdays">
                    <div class="weekday">日</div>
                    <div class="weekday">一</div>
                    <div class="weekday">二</div>
                    <div class="weekday">三</div>
                    <div class="weekday">四</div>
                    <div class="weekday">五</div>
                    <div class="weekday">六</div>
                </div>
                <div class="calendar-days" id="calendarDays">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
                <div class="calendar-legend">
                    <div class="legend-item">
                        <div class="legend-dot achieved"></div>
                        <span>已达标</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-dot partial"></div>
                        <span>部分完成</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-dot missed"></div>
                        <span>未达标</span>
                    </div>
                </div>
            </div>

            <!-- 今日记录列表 -->
            <div class="today-records">
                <div class="records-header">
                    <h3>今日记录</h3>
                    <span class="records-count" id="recordsCount">0 次</span>
                </div>
                <div class="records-list" id="recordsList">
                    <!-- 记录列表将通过 JavaScript 动态生成 -->
                </div>
            </div>

            <!-- 底部操作 -->
            <div class="bottom-actions">
                <button id="clearToday" class="action-btn danger">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                    </svg>
                    <span>清空今日记录</span>
                </button>

            </div>


        </div>

        <!-- 底部信息 -->
        <div class="footer">
            <div class="app-info">
                <span>WaterTime Desktop v<span id="appVersion">1.0.0</span></span>
            </div>
        </div>
    </div>

    <!-- 数据管理器 -->
    <script src="js/dataManager.js"></script>
    <!-- 主应用脚本 -->
    <script src="js/main.js"></script>
</body>
</html>
