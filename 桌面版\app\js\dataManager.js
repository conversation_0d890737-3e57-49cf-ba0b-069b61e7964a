// WaterTime 桌面版数据管理器
// 替代 Chrome storage API，提供完整的数据存储、验证和迁移功能

class WaterTimeDataManager {
    constructor() {
        this.isInitialized = false;
        this.electronAPI = window.electronAPI;
        this.cache = new Map(); // 内存缓存
        this.listeners = new Map(); // 数据变更监听器
    }

    // 单例模式
    static getInstance() {
        if (!WaterTimeDataManager.instance) {
            WaterTimeDataManager.instance = new WaterTimeDataManager();
        }
        return WaterTimeDataManager.instance;
    }

    // ==================== 初始化和配置 ====================

    async initialize() {
        if (this.isInitialized) return;

        try {
            console.log('[DataManager] 初始化数据管理器...');

            // 确保默认设置存在
            await this.ensureDefaultSettings();

            // 检查数据完整性
            await this.validateDataIntegrity();

            // 清理过期数据
            await this.cleanupOldData();

            // 设置每日重置检查
            this.setupDailyReset();

            this.isInitialized = true;
            console.log('[DataManager] 数据管理器初始化完成');

        } catch (error) {
            console.error('[DataManager] 初始化失败:', error);
            throw error;
        }
    }

    // 默认设置
    getDefaultSettings() {
        return {
            // 基础配置
            dailyTarget: 2000,
            singleDrink: 200,
            
            // 显示设置
            floatPosition: 'bottom-right',
            circleSize: 'medium',
            enableAnimations: true,
            showFloat: true,
            
            // 系统设置
            autoStartup: false,
            enableNotifications: true,
            notificationInterval: 60, // 分钟
            
            // 主题设置
            theme: 'default',
            language: 'zh-CN',
            
            // 数据设置
            dataRetentionDays: 30,
            autoBackup: true,
            
            // 版本信息
            dataVersion: '1.0.0',
            lastUpdated: new Date().toISOString()
        };
    }

    // 确保默认设置存在
    async ensureDefaultSettings() {
        try {
            const defaultSettings = this.getDefaultSettings();
            const existingSettings = await this.electronAPI.getStoreData('settings') || {};

            // 合并设置，保留用户自定义的值
            const mergedSettings = { ...defaultSettings, ...existingSettings };
            
            // 检查是否有新的设置项需要添加
            let hasNewSettings = false;
            for (const key in defaultSettings) {
                if (!(key in existingSettings)) {
                    hasNewSettings = true;
                    break;
                }
            }

            if (hasNewSettings) {
                await this.electronAPI.setStoreData('settings', mergedSettings);
                console.log('[DataManager] 已更新默认设置');
            }

        } catch (error) {
            console.error('[DataManager] 设置默认配置失败:', error);
        }
    }

    // ==================== 数据存储和读取 ====================

    // 获取设置
    async getSettings() {
        try {
            const cacheKey = 'settings';
            if (this.cache.has(cacheKey)) {
                return this.cache.get(cacheKey);
            }

            const settings = await this.electronAPI.getStoreData('settings') || this.getDefaultSettings();
            this.cache.set(cacheKey, settings);
            return settings;
        } catch (error) {
            console.error('[DataManager] 获取设置失败:', error);
            return this.getDefaultSettings();
        }
    }

    // 保存设置
    async saveSettings(newSettings) {
        try {
            const currentSettings = await this.getSettings();
            const mergedSettings = { ...currentSettings, ...newSettings };
            
            // 验证设置
            const validationErrors = this.validateSettings(mergedSettings);
            if (validationErrors.length > 0) {
                throw new Error(`设置验证失败: ${validationErrors.join(', ')}`);
            }

            // 更新时间戳
            mergedSettings.lastUpdated = new Date().toISOString();

            await this.electronAPI.setStoreData('settings', mergedSettings);
            this.cache.set('settings', mergedSettings);

            // 通知监听器
            this.notifyListeners('settings', mergedSettings);

            console.log('[DataManager] 设置已保存');
            return true;
        } catch (error) {
            console.error('[DataManager] 保存设置失败:', error);
            throw error;
        }
    }

    // 获取今日数据 - 简化版本，不使用缓存
    async getTodayData() {
        try {
            const todayKey = this.getTodayKey();
            console.log(`[DataManager] 获取今日数据，键名: ${todayKey}`);

            // 直接从存储获取，不使用缓存
            const todayData = await this.electronAPI.getStoreData(todayKey);
            console.log(`[DataManager] 从存储获取今日数据:`, todayData);

            const finalData = todayData || {
                total: 0,
                records: [],
                date: new Date().toISOString().split('T')[0],
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            console.log(`[DataManager] 今日数据获取完成:`, finalData);
            return finalData;
        } catch (error) {
            console.error('[DataManager] 获取今日数据失败:', error);
            return { total: 0, records: [], date: new Date().toISOString().split('T')[0] };
        }
    }

    // 保存今日数据 - 简化版本，不使用缓存
    async saveTodayData(data) {
        try {
            const todayKey = this.getTodayKey();
            const dataWithTimestamp = {
                ...data,
                updated: new Date().toISOString()
            };

            console.log(`[DataManager] 保存今日数据，键名: ${todayKey}`, dataWithTimestamp);

            await this.electronAPI.setStoreData(todayKey, dataWithTimestamp);
            // 不再使用缓存，让数据总是从存储中读取

            // 通知监听器
            this.notifyListeners('todayData', dataWithTimestamp);
            this.notifyListeners(todayKey, dataWithTimestamp);

            console.log('[DataManager] 今日数据已保存');
            return true;
        } catch (error) {
            console.error('[DataManager] 保存今日数据失败:', error);
            throw error;
        }
    }

    // 添加饮水记录
    async addDrinkRecord(amount) {
        try {
            const todayData = await this.getTodayData();
            const now = new Date();
            
            const record = {
                id: Date.now(),
                amount: amount,
                time: now.toISOString(),
                timestamp: now.getTime(),
                type: 'manual' // manual, auto, quick
            };

            todayData.records.push(record);
            todayData.total += amount;

            await this.saveTodayData(todayData);
            
            console.log(`[DataManager] 添加饮水记录: ${amount}ml`);
            return record;
        } catch (error) {
            console.error('[DataManager] 添加饮水记录失败:', error);
            throw error;
        }
    }

    // 删除饮水记录
    async removeDrinkRecord(recordId) {
        try {
            const todayData = await this.getTodayData();
            const recordIndex = todayData.records.findIndex(r => r.id === recordId);
            
            if (recordIndex === -1) {
                throw new Error('记录不存在');
            }

            const removedRecord = todayData.records.splice(recordIndex, 1)[0];
            todayData.total -= removedRecord.amount;

            await this.saveTodayData(todayData);
            
            console.log(`[DataManager] 删除饮水记录: ${removedRecord.amount}ml`);
            return removedRecord;
        } catch (error) {
            console.error('[DataManager] 删除饮水记录失败:', error);
            throw error;
        }
    }

    // 获取历史数据
    async getHistoryData(startDate, endDate) {
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const historyData = [];

            for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
                const dateKey = this.getDateKey(date);
                const dayData = await this.electronAPI.getStoreData(dateKey);
                
                if (dayData) {
                    historyData.push({
                        date: date.toISOString().split('T')[0],
                        ...dayData
                    });
                }
            }

            return historyData;
        } catch (error) {
            console.error('[DataManager] 获取历史数据失败:', error);
            return [];
        }
    }

    // ==================== 数据验证 ====================

    validateSettings(settings) {
        const errors = [];

        // 验证数值范围
        if (settings.dailyTarget < 500 || settings.dailyTarget > 10000) {
            errors.push('每日目标应在500-10000ml之间');
        }

        if (settings.singleDrink < 50 || settings.singleDrink > 2000) {
            errors.push('单次饮水量应在50-2000ml之间');
        }

        if (settings.notificationInterval < 5 || settings.notificationInterval > 480) {
            errors.push('通知间隔应在5-480分钟之间');
        }

        if (settings.dataRetentionDays < 7 || settings.dataRetentionDays > 365) {
            errors.push('数据保留天数应在7-365天之间');
        }

        // 验证枚举值
        const validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        if (!validPositions.includes(settings.floatPosition)) {
            errors.push('无效的浮动位置');
        }

        const validSizes = ['small', 'medium', 'large'];
        if (!validSizes.includes(settings.circleSize)) {
            errors.push('无效的圆圈大小');
        }

        return errors;
    }

    // ==================== 辅助方法 ====================

    getTodayKey() {
        const today = new Date();
        return `watertime_${today.getFullYear()}_${today.getMonth() + 1}_${today.getDate()}`;
    }

    getDateKey(date) {
        return `watertime_${date.getFullYear()}_${date.getMonth() + 1}_${date.getDate()}`;
    }

    // 数据变更监听
    addListener(key, callback) {
        if (!this.listeners.has(key)) {
            this.listeners.set(key, []);
        }
        this.listeners.get(key).push(callback);
    }

    removeListener(key, callback) {
        if (this.listeners.has(key)) {
            const callbacks = this.listeners.get(key);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    notifyListeners(key, data) {
        console.log('[DataManager] 通知监听器:', key, data);

        if (this.listeners.has(key)) {
            this.listeners.get(key).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('[DataManager] 监听器回调失败:', error);
                }
            });
        }
    }

    // ==================== 数据迁移和备份 ====================

    // 从Chrome扩展数据迁移
    async migrateFromChromeExtension(chromeData) {
        try {
            console.log('[DataManager] 开始从Chrome扩展迁移数据...');

            if (!chromeData || typeof chromeData !== 'object') {
                throw new Error('无效的Chrome扩展数据');
            }

            // 迁移设置
            const settings = this.getDefaultSettings();
            if (chromeData.dailyTarget) settings.dailyTarget = chromeData.dailyTarget;
            if (chromeData.singleDrink) settings.singleDrink = chromeData.singleDrink;
            if (chromeData.floatPosition) settings.floatPosition = chromeData.floatPosition;
            if (chromeData.circleSize) settings.circleSize = chromeData.circleSize;
            if (chromeData.enableAnimations !== undefined) settings.enableAnimations = chromeData.enableAnimations;

            await this.saveSettings(settings);

            // 迁移饮水记录
            let migratedRecords = 0;
            for (const key in chromeData) {
                if (key.startsWith('watertime_')) {
                    const recordData = chromeData[key];
                    if (recordData && recordData.total !== undefined) {
                        await this.electronAPI.setStoreData(key, {
                            ...recordData,
                            migrated: true,
                            migratedAt: new Date().toISOString()
                        });
                        migratedRecords++;
                    }
                }
            }

            console.log(`[DataManager] 数据迁移完成，迁移了 ${migratedRecords} 条记录`);
            return { success: true, migratedRecords };
        } catch (error) {
            console.error('[DataManager] 数据迁移失败:', error);
            throw error;
        }
    }

    // 导出数据
    async exportData() {
        try {
            console.log('[DataManager] 开始导出数据...');

            const exportData = {
                version: '1.0.0',
                exportTime: new Date().toISOString(),
                appVersion: await this.electronAPI.getAppVersion(),
                settings: await this.getSettings(),
                records: {}
            };

            // 获取所有饮水记录
            const allKeys = await this.getAllDataKeys();
            for (const key of allKeys) {
                if (key.startsWith('watertime_')) {
                    const data = await this.electronAPI.getStoreData(key);
                    if (data) {
                        exportData.records[key] = data;
                    }
                }
            }

            console.log(`[DataManager] 数据导出完成，包含 ${Object.keys(exportData.records).length} 条记录`);
            return exportData;
        } catch (error) {
            console.error('[DataManager] 数据导出失败:', error);
            throw error;
        }
    }

    // 导入数据
    async importData(importData) {
        try {
            console.log('[DataManager] 开始导入数据...');

            if (!importData || !importData.version) {
                throw new Error('无效的导入数据格式');
            }

            // 验证数据版本兼容性
            if (!this.isVersionCompatible(importData.version)) {
                throw new Error(`不兼容的数据版本: ${importData.version}`);
            }

            // 备份当前数据
            const backupData = await this.exportData();
            await this.electronAPI.setStoreData('backup_before_import', backupData);

            // 导入设置
            if (importData.settings) {
                await this.saveSettings(importData.settings);
            }

            // 导入记录
            let importedRecords = 0;
            if (importData.records) {
                for (const key in importData.records) {
                    await this.electronAPI.setStoreData(key, {
                        ...importData.records[key],
                        imported: true,
                        importedAt: new Date().toISOString()
                    });
                    importedRecords++;
                }
            }

            // 清理缓存
            this.cache.clear();

            console.log(`[DataManager] 数据导入完成，导入了 ${importedRecords} 条记录`);
            return { success: true, importedRecords };
        } catch (error) {
            console.error('[DataManager] 数据导入失败:', error);
            throw error;
        }
    }

    // 自动备份
    async createAutoBackup() {
        try {
            const settings = await this.getSettings();
            if (!settings.autoBackup) return;

            const backupData = await this.exportData();
            const backupKey = `auto_backup_${new Date().toISOString().split('T')[0]}`;

            await this.electronAPI.setStoreData(backupKey, backupData);
            console.log('[DataManager] 自动备份已创建');
        } catch (error) {
            console.error('[DataManager] 自动备份失败:', error);
        }
    }

    // ==================== 数据清理和维护 ====================

    // 验证数据完整性
    async validateDataIntegrity() {
        try {
            console.log('[DataManager] 验证数据完整性...');

            const issues = [];
            const allKeys = await this.getAllDataKeys();

            for (const key of allKeys) {
                if (key.startsWith('watertime_')) {
                    const data = await this.electronAPI.getStoreData(key);
                    if (data) {
                        // 验证数据结构
                        if (typeof data.total !== 'number' || data.total < 0) {
                            issues.push(`${key}: 无效的总量数据`);
                        }

                        if (!Array.isArray(data.records)) {
                            issues.push(`${key}: 无效的记录数组`);
                        } else {
                            // 验证记录完整性
                            let calculatedTotal = 0;
                            for (const record of data.records) {
                                if (!record.id || !record.amount || !record.time) {
                                    issues.push(`${key}: 记录缺少必要字段`);
                                }
                                calculatedTotal += record.amount || 0;
                            }

                            // 检查总量是否匹配
                            if (Math.abs(calculatedTotal - data.total) > 0.01) {
                                issues.push(`${key}: 总量与记录不匹配`);
                                // 自动修复
                                data.total = calculatedTotal;
                                await this.electronAPI.setStoreData(key, data);
                                console.log(`[DataManager] 已修复 ${key} 的总量数据`);
                            }
                        }
                    }
                }
            }

            if (issues.length > 0) {
                console.warn('[DataManager] 发现数据完整性问题:', issues);
            } else {
                console.log('[DataManager] 数据完整性验证通过');
            }

            return issues;
        } catch (error) {
            console.error('[DataManager] 数据完整性验证失败:', error);
            return [];
        }
    }

    // 清理过期数据
    async cleanupOldData() {
        try {
            const settings = await this.getSettings();
            const retentionDays = settings.dataRetentionDays || 30;
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

            console.log(`[DataManager] 清理 ${retentionDays} 天前的数据...`);

            const allKeys = await this.getAllDataKeys();
            const keysToRemove = [];

            for (const key of allKeys) {
                if (key.startsWith('watertime_')) {
                    const dateParts = key.replace('watertime_', '').split('_');
                    if (dateParts.length === 3) {
                        const recordDate = new Date(
                            parseInt(dateParts[0]),
                            parseInt(dateParts[1]) - 1,
                            parseInt(dateParts[2])
                        );

                        if (recordDate < cutoffDate) {
                            keysToRemove.push(key);
                        }
                    }
                }
            }

            // 删除过期数据
            for (const key of keysToRemove) {
                await this.electronAPI.setStoreData(key, null);
            }

            if (keysToRemove.length > 0) {
                console.log(`[DataManager] 清理了 ${keysToRemove.length} 条过期记录`);
            }

            return keysToRemove.length;
        } catch (error) {
            console.error('[DataManager] 清理过期数据失败:', error);
            return 0;
        }
    }

    // 设置每日重置
    setupDailyReset() {
        // 检查是否需要重置今日数据
        const checkDailyReset = async () => {
            try {
                const lastResetDate = await this.electronAPI.getStoreData('lastResetDate');
                const today = new Date().toISOString().split('T')[0];

                if (lastResetDate !== today) {
                    console.log('[DataManager] 检测到新的一天，执行每日重置');

                    // 创建自动备份
                    await this.createAutoBackup();

                    // 更新重置日期
                    await this.electronAPI.setStoreData('lastResetDate', today);

                    // 清理缓存中的今日数据
                    for (const [key] of this.cache) {
                        if (key.startsWith('today_')) {
                            this.cache.delete(key);
                        }
                    }

                    // 通知监听器
                    this.notifyListeners('dailyReset', { date: today });
                }
            } catch (error) {
                console.error('[DataManager] 每日重置检查失败:', error);
            }
        };

        // 立即检查一次
        checkDailyReset();

        // 每小时检查一次
        setInterval(checkDailyReset, 60 * 60 * 1000);
    }

    // 获取所有数据键（模拟方法，实际需要根据存储实现）
    async getAllDataKeys() {
        // 这里需要根据实际的存储实现来获取所有键
        // 暂时返回空数组，后续需要完善
        return [];
    }

    // 版本兼容性检查
    isVersionCompatible(version) {
        const currentVersion = '1.0.0';
        // 简单的版本兼容性检查
        return version === currentVersion || version.startsWith('1.0.');
    }
}

// 创建全局数据管理器实例（单例模式）
window.dataManager = WaterTimeDataManager.getInstance();

console.log('[DataManager] 全局数据管理器实例已创建');
