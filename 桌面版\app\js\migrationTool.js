// WaterTime 数据迁移工具
// 用于从Chrome扩展导入数据到桌面版

class MigrationTool {
    constructor() {
        this.dataManager = null;
    }

    async initialize() {
        if (!window.dataManager) {
            throw new Error('数据管理器未初始化');
        }
        this.dataManager = window.dataManager;
    }

    // 从文件导入Chrome扩展数据
    async importFromFile(file) {
        try {
            console.log('[MigrationTool] 开始从文件导入数据...');
            
            const text = await file.text();
            const importData = JSON.parse(text);
            
            // 验证数据格式
            if (!this.validateChromeData(importData)) {
                throw new Error('无效的Chrome扩展数据格式');
            }
            
            // 执行迁移
            const result = await this.dataManager.migrateFromChromeExtension(importData);
            
            console.log('[MigrationTool] 数据导入完成:', result);
            return result;
        } catch (error) {
            console.error('[MigrationTool] 数据导入失败:', error);
            throw error;
        }
    }

    // 从JSON对象导入Chrome扩展数据
    async importFromObject(chromeData) {
        try {
            console.log('[MigrationTool] 开始从对象导入数据...');
            
            if (!this.validateChromeData(chromeData)) {
                throw new Error('无效的Chrome扩展数据格式');
            }
            
            const result = await this.dataManager.migrateFromChromeExtension(chromeData);
            
            console.log('[MigrationTool] 数据导入完成:', result);
            return result;
        } catch (error) {
            console.error('[MigrationTool] 数据导入失败:', error);
            throw error;
        }
    }

    // 验证Chrome扩展数据格式
    validateChromeData(data) {
        if (!data || typeof data !== 'object') {
            return false;
        }

        // 检查是否包含Chrome扩展的特征数据
        const hasSettings = data.dailyTarget || data.singleDrink || data.floatPosition;
        const hasRecords = Object.keys(data).some(key => key.startsWith('watertime_'));
        
        return hasSettings || hasRecords;
    }

    // 生成迁移报告
    generateMigrationReport(chromeData) {
        const report = {
            totalRecords: 0,
            dateRange: { start: null, end: null },
            settings: {},
            recordsByMonth: {}
        };

        // 分析设置
        if (chromeData.dailyTarget) report.settings.dailyTarget = chromeData.dailyTarget;
        if (chromeData.singleDrink) report.settings.singleDrink = chromeData.singleDrink;
        if (chromeData.floatPosition) report.settings.floatPosition = chromeData.floatPosition;
        if (chromeData.circleSize) report.settings.circleSize = chromeData.circleSize;
        if (chromeData.enableAnimations !== undefined) report.settings.enableAnimations = chromeData.enableAnimations;

        // 分析记录
        const dates = [];
        Object.keys(chromeData).forEach(key => {
            if (key.startsWith('watertime_')) {
                const recordData = chromeData[key];
                if (recordData && recordData.total !== undefined) {
                    report.totalRecords++;
                    
                    // 解析日期
                    const dateParts = key.replace('watertime_', '').split('_');
                    if (dateParts.length === 3) {
                        const date = new Date(
                            parseInt(dateParts[0]),
                            parseInt(dateParts[1]) - 1,
                            parseInt(dateParts[2])
                        );
                        dates.push(date);
                        
                        // 按月统计
                        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        if (!report.recordsByMonth[monthKey]) {
                            report.recordsByMonth[monthKey] = 0;
                        }
                        report.recordsByMonth[monthKey]++;
                    }
                }
            }
        });

        // 计算日期范围
        if (dates.length > 0) {
            dates.sort((a, b) => a - b);
            report.dateRange.start = dates[0].toISOString().split('T')[0];
            report.dateRange.end = dates[dates.length - 1].toISOString().split('T')[0];
        }

        return report;
    }

    // 创建迁移预览
    createMigrationPreview(chromeData) {
        const report = this.generateMigrationReport(chromeData);
        
        return {
            summary: {
                totalRecords: report.totalRecords,
                dateRange: report.dateRange,
                hasSettings: Object.keys(report.settings).length > 0
            },
            details: {
                settings: report.settings,
                recordsByMonth: report.recordsByMonth
            },
            recommendations: this.generateRecommendations(report)
        };
    }

    // 生成迁移建议
    generateRecommendations(report) {
        const recommendations = [];

        if (report.totalRecords === 0) {
            recommendations.push({
                type: 'warning',
                message: '未发现饮水记录数据，请确认导入的文件是否正确'
            });
        } else if (report.totalRecords > 100) {
            recommendations.push({
                type: 'info',
                message: `将导入 ${report.totalRecords} 条记录，建议在网络良好时进行`
            });
        }

        if (Object.keys(report.settings).length === 0) {
            recommendations.push({
                type: 'warning',
                message: '未发现设置数据，将使用默认设置'
            });
        }

        if (report.dateRange.start && report.dateRange.end) {
            const daysDiff = Math.ceil((new Date(report.dateRange.end) - new Date(report.dateRange.start)) / (1000 * 60 * 60 * 24));
            if (daysDiff > 90) {
                recommendations.push({
                    type: 'info',
                    message: `数据跨度 ${daysDiff} 天，建议导入后进行数据清理`
                });
            }
        }

        return recommendations;
    }

    // 导出当前数据（用于备份）
    async exportCurrentData() {
        try {
            const exportData = await this.dataManager.exportData();
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `watertime_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('[MigrationTool] 数据导出完成');
            return true;
        } catch (error) {
            console.error('[MigrationTool] 数据导出失败:', error);
            throw error;
        }
    }

    // 创建迁移UI
    createMigrationUI() {
        const migrationHTML = `
            <div class="migration-tool">
                <h3>数据迁移工具</h3>
                <div class="migration-section">
                    <h4>从Chrome扩展导入</h4>
                    <input type="file" id="chromeDataFile" accept=".json" />
                    <button id="importBtn">导入数据</button>
                </div>
                <div class="migration-section">
                    <h4>备份当前数据</h4>
                    <button id="exportBtn">导出备份</button>
                </div>
                <div id="migrationStatus" class="migration-status"></div>
                <div id="migrationPreview" class="migration-preview"></div>
            </div>
        `;

        // 创建样式
        const style = document.createElement('style');
        style.textContent = `
            .migration-tool {
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 20px 0;
                background: #f9f9f9;
            }
            .migration-section {
                margin: 15px 0;
                padding: 15px;
                background: white;
                border-radius: 4px;
            }
            .migration-status {
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
            }
            .migration-status.success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .migration-status.error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .migration-preview {
                margin: 10px 0;
                padding: 10px;
                background: white;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
        `;
        document.head.appendChild(style);

        return migrationHTML;
    }

    // 绑定迁移UI事件
    bindMigrationEvents() {
        const fileInput = document.getElementById('chromeDataFile');
        const importBtn = document.getElementById('importBtn');
        const exportBtn = document.getElementById('exportBtn');
        const statusDiv = document.getElementById('migrationStatus');
        const previewDiv = document.getElementById('migrationPreview');

        if (fileInput) {
            fileInput.addEventListener('change', async (e) => {
                const file = e.target.files[0];
                if (file) {
                    try {
                        const text = await file.text();
                        const data = JSON.parse(text);
                        const preview = this.createMigrationPreview(data);
                        
                        previewDiv.innerHTML = `
                            <h4>导入预览</h4>
                            <p>记录数量: ${preview.summary.totalRecords}</p>
                            <p>日期范围: ${preview.summary.dateRange.start || '无'} 到 ${preview.summary.dateRange.end || '无'}</p>
                            <p>包含设置: ${preview.summary.hasSettings ? '是' : '否'}</p>
                        `;
                    } catch (error) {
                        previewDiv.innerHTML = '<p style="color: red;">文件格式错误</p>';
                    }
                }
            });
        }

        if (importBtn) {
            importBtn.addEventListener('click', async () => {
                const file = fileInput.files[0];
                if (!file) {
                    this.showStatus(statusDiv, 'error', '请选择要导入的文件');
                    return;
                }

                try {
                    this.showStatus(statusDiv, 'info', '正在导入数据...');
                    const result = await this.importFromFile(file);
                    this.showStatus(statusDiv, 'success', `导入成功！迁移了 ${result.migratedRecords} 条记录`);
                } catch (error) {
                    this.showStatus(statusDiv, 'error', `导入失败: ${error.message}`);
                }
            });
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', async () => {
                try {
                    await this.exportCurrentData();
                    this.showStatus(statusDiv, 'success', '数据导出成功！');
                } catch (error) {
                    this.showStatus(statusDiv, 'error', `导出失败: ${error.message}`);
                }
            });
        }
    }

    showStatus(statusDiv, type, message) {
        statusDiv.className = `migration-status ${type}`;
        statusDiv.textContent = message;
    }
}

// 创建全局迁移工具实例
window.migrationTool = new MigrationTool();
