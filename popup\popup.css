/* WaterTime 弹窗样式 */

/* 基础重置和字体 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 320px;
    height: 400px;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #E0F6FF 0%, #B0E0E6 50%, #87CEEB 100%);
    color: #2F4F4F;
    overflow: hidden;
    user-select: none;
}

/* 主容器 */
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    position: relative;
}

/* 标题区域 */
.header {
    margin-bottom: 20px;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #2F4F4F;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    letter-spacing: 1px;
}

/* 进度圈容器 */
.progress-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

/* SVG进度圈 */
.progress-circle {
    transform: scale(0.9);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* 进度圆弧动画 */
.progress-arc {
    transition: stroke-dashoffset 0.6s ease-in-out;
    /* 添加发光效果 */
    filter: drop-shadow(0 0 4px rgba(0, 191, 255, 0.3));
}

/* 进度圈发光动画 */
.progress-arc.glowing {
    animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
    0% {
        filter: drop-shadow(0 0 4px rgba(0, 191, 255, 0.3));
    }
    100% {
        filter: drop-shadow(0 0 8px rgba(0, 191, 255, 0.6));
    }
}

/* 进度圈内容 */
.progress-content {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 140px;
}

/* 进度文字 */
.progress-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.percentage {
    font-size: 24px;
    font-weight: bold;
    color: #00BFFF;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.amount {
    font-size: 14px;
    color: #5F9EA0;
    margin-top: 2px;
}

/* 水杯按钮 */
.water-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

/* 悬停效果增强 */
.water-button:hover {
    transform: scale(1.15) translateY(-2px);
    background: rgba(255, 255, 255, 0.4);
    box-shadow:
        0 8px 20px rgba(0, 191, 255, 0.4),
        0 4px 12px rgba(135, 206, 235, 0.3);
    border-color: rgba(0, 191, 255, 0.5);
}

/* 按下效果 */
.water-button:active {
    transform: scale(1.05) translateY(0);
    transition: all 0.1s ease;
}

/* 按钮波纹效果 */
.water-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(0, 191, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.water-button:active::before {
    width: 100px;
    height: 100px;
}

/* 水杯图标 */
.water-icon {
    display: block;
    transition: all 0.3s ease;
}

/* 水面和水滴动画 */
.water-surface {
    animation: waterRipple 2s ease-in-out infinite;
}

.water-drop {
    animation: dropFloat 3s ease-in-out infinite;
}

@keyframes waterRipple {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes dropFloat {
    0%, 100% { transform: translateY(0); opacity: 0.6; }
    50% { transform: translateY(-2px); opacity: 1; }
}

/* 点击动画效果增强 */
.water-button.clicked {
    animation: clickEffect 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes clickEffect {
    0% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1);
    }
    25% {
        transform: scale(1.3) rotate(5deg);
        filter: brightness(1.2);
    }
    50% {
        transform: scale(1.1) rotate(-3deg);
        filter: brightness(1.1);
    }
    75% {
        transform: scale(1.2) rotate(2deg);
        filter: brightness(1.15);
    }
    100% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1);
    }
}

/* 成功点击后的庆祝动画 */
.water-button.success {
    animation: successPulse 1s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(50, 205, 50, 0.7);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(50, 205, 50, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(50, 205, 50, 0);
    }
}

/* 底部信息区域 */
.footer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

/* 统计信息 */
.stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* 统计信息悬停效果 */
.stats:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(135, 206, 235, 0.2);
}

/* 统计信息背景动画 */
.stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stats:hover::before {
    left: 100%;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.stat-label {
    font-size: 11px;
    color: #5F9EA0;
    margin-bottom: 2px;
    font-weight: 500;
}

.stat-value {
    font-size: 13px;
    font-weight: bold;
    color: #2F4F4F;
}

/* 设置按钮 */
.settings-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 12px;
    color: #5F9EA0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.3);
    color: #2F4F4F;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-button:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 15px;
    }
    
    .progress-circle {
        transform: scale(0.8);
    }
    
    .title {
        font-size: 18px;
    }
    
    .percentage {
        font-size: 20px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .progress-arc {
        stroke: #0066CC;
    }
    
    .percentage {
        color: #0066CC;
    }
    
    .stat-value {
        color: #000000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .progress-arc,
    .water-button,
    .water-icon,
    .settings-button {
        transition: none;
    }
    
    .water-surface,
    .water-drop {
        animation: none;
    }
}
