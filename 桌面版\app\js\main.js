// WaterTime 桌面版主窗口脚本

class WaterTimeApp {
    constructor() {
        this.dataManager = null;
        this.data = {
            dailyTarget: 2000,
            singleDrink: 200,
            todayTotal: 0,
            todayRecords: [],
            currentDate: new Date()
        };

        this.init();
    }

    async init() {
        try {
            console.log('[WaterTimeApp] 开始初始化主窗口...');

            // 使用全局数据管理器实例
            this.dataManager = window.dataManager;
            console.log('[WaterTimeApp] 使用全局数据管理器实例');

            // 初始化数据管理器
            await this.dataManager.initialize();
            console.log('[WaterTimeApp] 数据管理器初始化完成');

            // 加载数据
            await this.loadData();
            console.log('[WaterTimeApp] 数据加载完成');

            // 设置数据变更监听
            this.setupDataListeners();

            // 绑定事件
            this.bindEvents();
            console.log('[WaterTimeApp] 事件绑定完成');

            // 更新UI
            this.updateUI();
            this.renderCalendar();
            console.log('[WaterTimeApp] 界面更新完成');

            // 加载应用版本
            this.loadAppVersion();

            console.log('[WaterTimeApp] 应用初始化完成');
        } catch (error) {
            console.error('[WaterTimeApp] 应用初始化失败:', error);
            this.showFeedback('error', '应用初始化失败，请重启应用');
        }
    }

    // 等待数据管理器加载
    async waitForDataManager() {
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时

        while (!window.dataManager && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.dataManager) {
            throw new Error('数据管理器加载超时');
        }

        this.dataManager = window.dataManager;
    }

    // 设置数据变更监听
    setupDataListeners() {
        // 监听设置变更
        this.dataManager.addListener('settings', (settings) => {
            console.log('[WaterTimeApp] 设置已更新:', settings);
            this.data.dailyTarget = settings.dailyTarget;
            this.data.singleDrink = settings.singleDrink;
            this.updateUI();
        });

        // 监听今日数据变更
        this.dataManager.addListener('todayData', (todayData) => {
            console.log('[WaterTimeApp] 今日数据已更新:', todayData);
            this.data.todayTotal = todayData.total;
            this.data.todayRecords = todayData.records;
            this.updateUI();
            this.renderRecords();
        });

        // 监听每日重置
        this.dataManager.addListener('dailyReset', (resetInfo) => {
            console.log('[WaterTimeApp] 检测到每日重置:', resetInfo);
            this.loadData(); // 重新加载数据
            this.showFeedback('info', '新的一天开始了！');
        });
    }

    // 加载数据
    async loadData() {
        try {
            // 加载设置
            const settings = await this.dataManager.getSettings();
            this.data.dailyTarget = settings.dailyTarget;
            this.data.singleDrink = settings.singleDrink;

            // 更新快速设置输入框
            document.getElementById('dailyTarget').value = settings.dailyTarget;
            document.getElementById('singleAmount').value = settings.singleDrink;
            document.getElementById('quickAmount').textContent = settings.singleDrink + 'ml';

            // 加载今日数据
            const todayData = await this.dataManager.getTodayData();
            this.data.todayTotal = todayData.total;
            this.data.todayRecords = todayData.records;

            console.log('[WaterTimeApp] 数据加载完成');
        } catch (error) {
            console.error('[WaterTimeApp] 加载数据失败:', error);
            this.showFeedback('error', '数据加载失败');
        }
    }

    // 保存数据（现在通过数据管理器）
    async saveData() {
        try {
            // 保存今日数据
            await this.dataManager.saveTodayData({
                total: this.data.todayTotal,
                records: this.data.todayRecords,
                date: new Date().toISOString().split('T')[0]
            });

            console.log('[WaterTimeApp] 数据保存完成');
        } catch (error) {
            console.error('[WaterTimeApp] 保存数据失败:', error);
            this.showFeedback('error', '数据保存失败');
        }
    }

    // 绑定事件
    bindEvents() {


        // 快速操作按钮
        document.getElementById('quickDrink').addEventListener('click', () => {
            this.addDrinkRecord();
        });

        document.getElementById('quickUndo').addEventListener('click', () => {
            this.undoLastRecord();
        });

        // 日历导航
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.changeMonth(-1);
        });

        document.getElementById('nextMonth').addEventListener('click', () => {
            this.changeMonth(1);
        });



        // 监听来自主进程的IPC事件
        this.setupIpcListeners();

        // 清空今日记录
        document.getElementById('clearToday').addEventListener('click', () => {
            this.clearTodayRecords();
        });

        // 快速设置
        document.getElementById('dailyTarget').addEventListener('change', (e) => {
            this.updateSetting('dailyTarget', parseInt(e.target.value));
        });

        document.getElementById('singleAmount').addEventListener('change', (e) => {
            this.updateSetting('singleDrink', parseInt(e.target.value));
            // 更新快速记录按钮显示的数量
            document.getElementById('quickAmount').textContent = e.target.value + 'ml';
        });

        // 设置输入框
        document.getElementById('targetInput').addEventListener('change', async (e) => {
            try {
                const newTarget = parseInt(e.target.value);
                await this.dataManager.saveSettings({ dailyTarget: newTarget });
                this.showFeedback('success', '每日目标已更新');
            } catch (error) {
                console.error('[WaterTimeApp] 更新每日目标失败:', error);
                this.showFeedback('error', '设置保存失败');
                // 恢复原值
                e.target.value = this.data.dailyTarget;
            }
        });

        document.getElementById('singleDrinkInput').addEventListener('change', async (e) => {
            try {
                const newAmount = parseInt(e.target.value);
                await this.dataManager.saveSettings({ singleDrink: newAmount });
                this.showFeedback('success', '单次饮水量已更新');
            } catch (error) {
                console.error('[WaterTimeApp] 更新单次饮水量失败:', error);
                this.showFeedback('error', '设置保存失败');
                // 恢复原值
                e.target.value = this.data.singleDrink;
            }
        });

        // 复选框设置
        document.getElementById('autoStartup').addEventListener('change', (e) => {
            // 这里可以通过 IPC 通信设置开机自启动
            console.log('开机自启动:', e.target.checked);
        });

        document.getElementById('showFloat').addEventListener('change', (e) => {
            // 这里可以通过 IPC 通信控制浮动窗口显示
            console.log('显示浮动圆圈:', e.target.checked);
        });
    }

    // 设置IPC事件监听
    setupIpcListeners() {
        // 监听快速记录事件
        window.electronAPI.onQuickDrink(() => {
            console.log('[WaterTimeApp] 收到快速记录事件');
            this.addDrinkRecord();
        });
    }

    // 添加饮水记录
    async addDrinkRecord() {
        try {
            const amount = this.data.singleDrink;
            const record = await this.dataManager.addDrinkRecord(amount);

            // 数据管理器会自动更新并通知监听器，这里只需要显示反馈
            this.showFeedback('success', `已记录 ${amount}ml 饮水`);

            console.log('[WaterTimeApp] 饮水记录已添加:', record);
        } catch (error) {
            console.error('[WaterTimeApp] 添加饮水记录失败:', error);
            this.showFeedback('error', '记录失败，请重试');
        }
    }

    // 撤销最后一条记录
    async undoLastRecord() {
        try {
            if (this.data.todayRecords.length === 0) {
                this.showFeedback('warning', '没有可撤销的记录');
                return;
            }

            const lastRecord = this.data.todayRecords[this.data.todayRecords.length - 1];
            const removedRecord = await this.dataManager.removeDrinkRecord(lastRecord.id);

            this.showFeedback('info', `已撤销 ${removedRecord.amount}ml 记录`);

            console.log('[WaterTimeApp] 饮水记录已撤销:', removedRecord);
        } catch (error) {
            console.error('[WaterTimeApp] 撤销记录失败:', error);
            this.showFeedback('error', '撤销失败，请重试');
        }
    }

    // 更新UI
    updateUI() {
        const percentage = Math.min(100, Math.round((this.data.todayTotal / this.data.dailyTarget) * 100));
        const remaining = Math.max(0, this.data.dailyTarget - this.data.todayTotal);

        // 更新概览卡片
        document.getElementById('todayPercentage').textContent = `${percentage}%`;
        document.getElementById('todayAmount').textContent = `${this.data.todayTotal}ml`;
        document.getElementById('targetAmount').textContent = `${this.data.dailyTarget}ml`;
        document.getElementById('remainingAmount').textContent = `${remaining}ml`;

        // 更新记录数量
        document.getElementById('recordsCount').textContent = `${this.data.todayRecords.length} 次`;

        // 更新设置输入框
        document.getElementById('targetInput').value = this.data.dailyTarget;
        document.getElementById('singleDrinkInput').value = this.data.singleDrink;

        this.updateQuickAmount();
        this.renderRecords();
    }

    // 更新快速记录按钮显示的量
    updateQuickAmount() {
        document.getElementById('quickAmount').textContent = `${this.data.singleDrink}ml`;
    }

    // 渲染日历
    async renderCalendar() {
        const year = this.data.currentDate.getFullYear();
        const month = this.data.currentDate.getMonth();

        // 更新标题
        document.getElementById('currentMonth').textContent =
            `${year}年${month + 1}月`;

        // 获取当月的第一天和最后一天
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay()); // 从周日开始

        const calendarDays = document.getElementById('calendarDays');
        calendarDays.innerHTML = '';

        // 获取历史数据
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 41); // 6周
        const historyData = await this.getHistoryDataForCalendar(startDate, endDate);

        // 生成42天的日历（6周）
        for (let i = 0; i < 42; i++) {
            const currentDate = new Date(startDate);
            currentDate.setDate(startDate.getDate() + i);

            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = currentDate.getDate();

            // 添加样式类
            if (currentDate.getMonth() !== month) {
                dayElement.classList.add('other-month');
            }

            // 今天
            const today = new Date();
            if (currentDate.toDateString() === today.toDateString()) {
                dayElement.classList.add('today');
            }

            // 添加饮水数据状态
            const dateKey = this.formatDateKey(currentDate);
            const dayData = historyData[dateKey];
            if (dayData && currentDate <= today) {
                const percentage = (dayData.total / this.data.dailyTarget) * 100;
                if (percentage >= 100) {
                    dayElement.classList.add('achieved');
                } else if (percentage >= 50) {
                    dayElement.classList.add('partial');
                } else if (percentage > 0) {
                    dayElement.classList.add('missed');
                }

                // 添加点击事件显示详情
                dayElement.addEventListener('click', () => {
                    this.showDayDetails(currentDate, dayData);
                });
            }

            calendarDays.appendChild(dayElement);
        }
    }

    // 获取日历历史数据
    async getHistoryDataForCalendar(startDate, endDate) {
        try {
            const historyData = await this.dataManager.getHistoryData(
                startDate.toISOString().split('T')[0],
                endDate.toISOString().split('T')[0]
            );

            const dataMap = {};
            historyData.forEach(dayData => {
                dataMap[dayData.date] = dayData;
            });

            return dataMap;
        } catch (error) {
            console.error('[WaterTimeApp] 获取历史数据失败:', error);
            return {};
        }
    }

    // 格式化日期键
    formatDateKey(date) {
        return date.toISOString().split('T')[0];
    }

    // 显示某天的详情
    showDayDetails(date, dayData) {
        const dateStr = date.toLocaleDateString('zh-CN');
        const percentage = Math.round((dayData.total / this.data.dailyTarget) * 100);

        this.showFeedback('info',
            `${dateStr}: ${dayData.total}ml (${percentage}%) - ${dayData.records.length}次记录`
        );
    }

    // 渲染记录列表
    renderRecords() {
        const recordsList = document.getElementById('recordsList');

        if (this.data.todayRecords.length === 0) {
            recordsList.innerHTML = `
                <div class="records-empty">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z"/>
                    </svg>
                    <div>今日暂无饮水记录</div>
                    <div>点击"快速记录"开始记录吧！</div>
                </div>
            `;
            return;
        }

        const recordsHTML = this.data.todayRecords
            .sort((a, b) => b.timestamp - a.timestamp)
            .map(record => {
                const time = new Date(record.time).toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                return `
                    <div class="record-item" data-record-id="${record.id}">
                        <span class="record-time">${time}</span>
                        <span class="record-amount">${record.amount}ml</span>
                        <button class="record-delete" onclick="app.deleteRecord('${record.id}')">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                            </svg>
                        </button>
                    </div>
                `;
            })
            .join('');

        recordsList.innerHTML = recordsHTML;
    }

    // 删除单条记录
    async deleteRecord(recordId) {
        try {
            await this.dataManager.removeDrinkRecord(recordId);
            this.showFeedback('success', '记录已删除');
        } catch (error) {
            console.error('[WaterTimeApp] 删除记录失败:', error);
            this.showFeedback('error', '删除失败，请重试');
        }
    }

    // 更新设置
    async updateSetting(key, value) {
        try {
            const settings = await this.dataManager.getSettings();
            settings[key] = value;
            await this.dataManager.saveSettings(settings);

            // 如果是每日目标，更新显示
            if (key === 'dailyTarget') {
                this.data.dailyTarget = value;
                this.updateTodayOverview();
            }

            // 如果是单次饮水量，更新设置
            if (key === 'singleDrink') {
                this.data.singleDrink = value;
            }

            // 强制同步数据到浮窗
            await this.syncDataToFloat();

            console.log(`[WaterTimeApp] 设置已更新: ${key} = ${value}`);
        } catch (error) {
            console.error('[WaterTimeApp] 更新设置失败:', error);
        }
    }

    // 强制同步数据到浮窗
    async syncDataToFloat() {
        try {
            // 通过IPC通知浮窗刷新数据
            if (window.electronAPI && window.electronAPI.syncDataToFloat) {
                await window.electronAPI.syncDataToFloat();
            }
        } catch (error) {
            console.error('[WaterTimeApp] 同步数据到浮窗失败:', error);
        }
    }

    // 清空今日记录
    async clearTodayRecords() {
        if (this.data.todayRecords.length === 0) {
            this.showFeedback('warning', '今日暂无记录');
            return;
        }

        if (confirm('确定要清空今日所有记录吗？此操作不可恢复。')) {
            try {
                await this.dataManager.saveTodayData({
                    total: 0,
                    records: [],
                    date: new Date().toISOString().split('T')[0]
                });

                this.showFeedback('success', '今日记录已清空');
            } catch (error) {
                console.error('[WaterTimeApp] 清空记录失败:', error);
                this.showFeedback('error', '清空失败，请重试');
            }
        }
    }

    // 导出数据
    async exportData() {
        try {
            const exportData = await this.dataManager.exportData();

            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `watertime_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showFeedback('success', '数据导出成功');
        } catch (error) {
            console.error('[WaterTimeApp] 导出数据失败:', error);
            this.showFeedback('error', '导出失败，请重试');
        }
    }

    // 导入数据
    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const importData = JSON.parse(text);

                if (confirm('导入数据将覆盖当前数据，确定继续吗？建议先导出备份。')) {
                    const result = await this.dataManager.importData(importData);
                    this.showFeedback('success', `导入成功！导入了 ${result.importedRecords} 条记录`);

                    // 重新加载数据
                    await this.loadData();
                    this.updateUI();
                    this.renderCalendar();
                }
            } catch (error) {
                console.error('[WaterTimeApp] 导入数据失败:', error);
                this.showFeedback('error', '导入失败，请检查文件格式');
            }
        };

        input.click();
    }

    // 清空所有数据
    async clearAllData() {
        if (confirm('确定要清空所有数据吗？此操作不可恢复，建议先导出备份。')) {
            if (confirm('再次确认：这将删除所有饮水记录和设置，确定继续吗？')) {
                try {
                    // 这里需要实现清空所有数据的功能
                    this.showFeedback('info', '清空功能开发中...');
                } catch (error) {
                    console.error('[WaterTimeApp] 清空数据失败:', error);
                    this.showFeedback('error', '清空失败，请重试');
                }
            }
        }
    }

    // 切换月份
    changeMonth(delta) {
        this.data.currentDate.setMonth(this.data.currentDate.getMonth() + delta);
        this.renderCalendar();
    }

    // 显示反馈信息
    showFeedback(type, message) {
        // 这里可以实现一个简单的通知系统
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 可以添加视觉反馈，比如临时显示一个提示框
        const feedback = document.createElement('div');
        feedback.className = `feedback feedback-${type}`;
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        switch (type) {
            case 'success':
                feedback.style.background = '#28a745';
                break;
            case 'warning':
                feedback.style.background = '#ffc107';
                feedback.style.color = '#333';
                break;
            case 'info':
                feedback.style.background = '#17a2b8';
                break;
        }
        
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            feedback.remove();
        }, 3000);
    }

    // 加载应用版本
    async loadAppVersion() {
        try {
            const version = await window.electronAPI.getAppVersion();
            document.getElementById('appVersion').textContent = version;
        } catch (error) {
            console.error('获取应用版本失败:', error);
        }
    }
}

// 添加反馈动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    
    .record-item:last-child {
        border-bottom: none;
    }
    
    .record-time {
        color: #666;
        font-size: 14px;
    }
    
    .record-amount {
        color: #4facfe;
        font-weight: 500;
        font-size: 14px;
    }
`;
document.head.appendChild(style);

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new WaterTimeApp();
    window.app = app; // 全局访问
});
