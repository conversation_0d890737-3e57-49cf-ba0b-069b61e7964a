<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime 使用说明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #E0F6FF 0%, #B9E6FF 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #00BFFF;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 191, 255, 0.2);
        }

        .header .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .version {
            background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
        }

        .section {
            background: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #00BFFF;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #00BFFF;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #1E90FF;
            font-size: 1.3em;
            margin: 25px 0 15px 0;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: rgba(0, 191, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #00BFFF;
        }

        .feature-card h4 {
            color: #00BFFF;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .step-list {
            counter-reset: step-counter;
        }

        .step-item {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: rgba(0, 191, 255, 0.05);
            border-radius: 8px;
            position: relative;
            padding-left: 60px;
        }

        .step-item::before {
            content: counter(step-counter);
            position: absolute;
            left: 15px;
            top: 15px;
            background: #00BFFF;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .button-demo {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
            color: white;
            border-radius: 8px;
            text-decoration: none;
            margin: 5px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 191, 255, 0.3);
        }

        .button-demo.secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #00BFFF;
            border: 2px solid #00BFFF;
        }

        .button-demo.danger {
            background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
            color: white;
        }

        .screenshot {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin: 15px 0;
        }

        .tip {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #FFC107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .warning {
            background: rgba(255, 107, 107, 0.1);
            border-left: 4px solid #FF6B6B;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }

        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .toc {
            background: rgba(0, 191, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .toc h3 {
            color: #00BFFF;
            margin-bottom: 15px;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 8px 0;
        }

        .toc a {
            color: #1E90FF;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.2s;
        }

        .toc a:hover {
            background: rgba(0, 191, 255, 0.2);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .step-item {
                padding-left: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>💧 WaterTime</h1>
            <p class="subtitle">智能饮水提醒与记录插件</p>
            <span class="version">v2.0.0</span>
        </div>

        <!-- 目录 -->
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">功能概览</a></li>
                <li><a href="#installation">安装指南</a></li>
                <li><a href="#float-widget">浮动圆圈使用</a></li>
                <li><a href="#popup-interface">弹窗界面使用</a></li>
                <li><a href="#calendar">日历功能</a></li>
                <li><a href="#settings">设置配置</a></li>
                <li><a href="#tips">使用技巧</a></li>
                <li><a href="#faq">常见问题</a></li>
            </ul>
        </div>

        <!-- 功能概览 -->
        <div class="section" id="overview">
            <h2>🌟 功能概览</h2>
            <p>WaterTime 是一款专业的饮水提醒与记录Chrome插件，帮助您养成健康的饮水习惯。</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🎯 智能记录</h4>
                    <p>一键记录饮水量，自动统计每日进度，支持快速记录和撤回功能。</p>
                </div>
                <div class="feature-card">
                    <h4>📅 日历视图</h4>
                    <p>直观的日历界面，查看历史饮水记录，点击日期查看详细数据。</p>
                </div>
                <div class="feature-card">
                    <h4>⚙️ 个性化设置</h4>
                    <p>自定义每日目标、单次饮水量、浮动位置等，满足个人需求。</p>
                </div>
                <div class="feature-card">
                    <h4>🎨 美观界面</h4>
                    <p>现代化设计，毛玻璃效果，支持拖拽和动画，提供优质用户体验。</p>
                </div>
            </div>
        </div>

        <!-- 安装指南 -->
        <div class="section" id="installation">
            <h2>📦 安装指南</h2>
            
            <h3>方法一：Chrome应用商店安装（推荐）</h3>
            <div class="step-list">
                <div class="step-item">打开Chrome浏览器，访问Chrome网上应用店</div>
                <div class="step-item">搜索"WaterTime"或"饮水提醒"</div>
                <div class="step-item">点击"添加至Chrome"按钮</div>
                <div class="step-item">确认安装，等待安装完成</div>
            </div>

            <h3>方法二：开发者模式安装</h3>
            <div class="step-list">
                <div class="step-item">下载WaterTime插件源码包</div>
                <div class="step-item">打开Chrome浏览器，进入扩展程序管理页面（chrome://extensions/）</div>
                <div class="step-item">开启右上角的"开发者模式"</div>
                <div class="step-item">点击"加载已解压的扩展程序"</div>
                <div class="step-item">选择WaterTime文件夹，完成安装</div>
            </div>

            <div class="tip">
                <strong>💡 提示：</strong>安装完成后，建议将WaterTime插件固定到工具栏，方便快速访问。
            </div>
        </div>

        <!-- 浮动圆圈使用 -->
        <div class="section" id="float-widget">
            <h2>🎯 浮动圆圈使用</h2>
            <p>浮动圆圈是WaterTime的核心功能，提供便捷的饮水记录方式。</p>

            <h3>基本操作</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🖱️ 快速点击</h4>
                    <p>快速点击浮动圆圈（<0.5秒），立即记录一次饮水量。</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 拖拽移动</h4>
                    <p>按住圆圈拖拽到合适位置，松手后自动保存位置。</p>
                </div>
                <div class="feature-card">
                    <h4>📊 进度显示</h4>
                    <p>圆圈内显示当前饮水进度，颜色变化反映完成状态。</p>
                </div>
                <div class="feature-card">
                    <h4>🎨 视觉反馈</h4>
                    <p>点击时有动画效果，拖拽时透明度变化，提供清晰反馈。</p>
                </div>
            </div>

            <h3>防误触机制</h3>
            <div class="step-list">
                <div class="step-item">
                    <strong>时间检测：</strong>拖拽时间超过0.5秒不会触发记录
                </div>
                <div class="step-item">
                    <strong>距离检测：</strong>拖拽距离超过5像素不会触发记录
                </div>
                <div class="step-item">
                    <strong>视觉提示：</strong>长时间拖拽时显示橙色阴影提示
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ 注意：</strong>如果按住圆圈超过0.5秒，松手时不会记录饮水量，这是为了防止拖拽时的误操作。
            </div>
        </div>

        <!-- 弹窗界面使用 -->
        <div class="section" id="popup-interface">
            <h2>🖥️ 弹窗界面使用</h2>
            <p>点击工具栏中的WaterTime图标，打开详细的管理界面。</p>

            <h3>主界面功能</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📈 今日进度</h4>
                    <p>显示当前饮水量、完成百分比和剩余目标量。</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ 快速操作</h4>
                    <p>快速记录、快速撤回按钮，位于日历上方。</p>
                </div>
                <div class="feature-card">
                    <h4>📅 饮水日历</h4>
                    <p>月度日历视图，不同颜色表示不同完成状态。</p>
                </div>
                <div class="feature-card">
                    <h4>🗑️ 管理操作</h4>
                    <p>删除当天记录、设置配置等管理功能。</p>
                </div>
            </div>

            <h3>按钮说明</h3>
            <div style="margin: 20px 0;">
                <span class="button-demo">快速记录 200ml</span>
                <span class="button-demo secondary">快速撤回</span>
                <span class="button-demo danger">删除当天记录</span>
                <span class="button-demo secondary">设置</span>
            </div>

            <div class="step-list">
                <div class="step-item">
                    <strong>快速记录：</strong>一键添加预设的饮水量（默认200ml）
                </div>
                <div class="step-item">
                    <strong>快速撤回：</strong>撤销最近一次的饮水记录
                </div>
                <div class="step-item">
                    <strong>删除当天记录：</strong>清空当天所有饮水记录（需确认）
                </div>
                <div class="step-item">
                    <strong>设置：</strong>打开全屏设置页面，配置个人偏好
                </div>
            </div>
        </div>

        <!-- 日历功能 -->
        <div class="section" id="calendar">
            <h2>📅 日历功能</h2>
            <p>日历是查看历史饮水记录的重要工具，支持点击选择和数据查看。</p>

            <h3>日历状态说明</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🟢 已达标</h4>
                    <p>绿色圆点：当天饮水量达到或超过目标值。</p>
                </div>
                <div class="feature-card">
                    <h4>🟡 部分完成</h4>
                    <p>黄色圆点：当天饮水量达到目标的50%-99%。</p>
                </div>
                <div class="feature-card">
                    <h4>🔴 未达标</h4>
                    <p>红色圆点：当天饮水量少于目标的50%。</p>
                </div>
                <div class="feature-card">
                    <h4>⚪ 无记录</h4>
                    <p>无标记：当天没有饮水记录。</p>
                </div>
            </div>

            <h3>日期选择功能</h3>
            <div class="step-list">
                <div class="step-item">
                    <strong>点击日期：</strong>点击任意日期查看该天的饮水数据
                </div>
                <div class="step-item">
                    <strong>选中状态：</strong>选中的日期显示红色高亮边框
                </div>
                <div class="step-item">
                    <strong>今日特殊：</strong>今天的日期有蓝色背景标识
                </div>
                <div class="step-item">
                    <strong>数据显示：</strong>选择日期后，上方进度区域显示对应数据
                </div>
            </div>

            <h3>操作权限</h3>
            <div class="tip">
                <strong>💡 重要：</strong>只有选择今天的日期时，才能进行记录、撤回、删除等操作。选择历史日期时，所有操作按钮会被禁用，确保历史数据安全。
            </div>

            <div class="step-list">
                <div class="step-item">
                    <strong>今日操作：</strong>选择今天时，所有按钮正常可用
                </div>
                <div class="step-item">
                    <strong>历史只读：</strong>选择历史日期时，按钮变灰禁用
                </div>
                <div class="step-item">
                    <strong>错误提示：</strong>尝试在历史日期操作时显示提示信息
                </div>
            </div>
        </div>

        <!-- 设置配置 -->
        <div class="section" id="settings">
            <h2>⚙️ 设置配置</h2>
            <p>通过设置页面，您可以个性化配置WaterTime的各项功能。</p>

            <h3>饮水目标设置</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🎯 每日目标</h4>
                    <p>设置每日饮水目标量，建议成人每日2000-2500ml。</p>
                </div>
                <div class="feature-card">
                    <h4>💧 单次饮水量</h4>
                    <p>设置快速记录时的默认饮水量，常见值为200ml。</p>
                </div>
            </div>

            <h3>界面设置</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📍 浮动位置</h4>
                    <p>选择浮动圆圈的默认位置：右下角、右上角、左下角、左上角。</p>
                </div>
                <div class="feature-card">
                    <h4>📏 图标大小</h4>
                    <p>调整浮动圆圈的大小：小(80px)、中(100px)、大(120px)。</p>
                </div>
                <div class="feature-card">
                    <h4>🎨 启用动画</h4>
                    <p>开启或关闭界面动画效果，关闭可提升性能。</p>
                </div>
            </div>

            <h3>设置步骤</h3>
            <div class="step-list">
                <div class="step-item">点击弹窗界面底部的"设置"按钮</div>
                <div class="step-item">在全屏设置页面中调整各项参数</div>
                <div class="step-item">点击"保存设置"按钮应用更改</div>
                <div class="step-item">如需恢复默认，点击"重置设置"按钮</div>
            </div>

            <div class="tip">
                <strong>💡 提示：</strong>设置更改会立即生效，无需重启浏览器。所有设置数据会自动同步保存。
            </div>
        </div>

        <!-- 使用技巧 -->
        <div class="section" id="tips">
            <h2>💡 使用技巧</h2>

            <h3>高效记录技巧</h3>
            <div class="step-list">
                <div class="step-item">
                    <strong>快速点击：</strong>养成快速点击浮动圆圈的习惯，避免长按误触
                </div>
                <div class="step-item">
                    <strong>合理位置：</strong>将浮动圆圈放在不影响浏览但容易点击的位置
                </div>
                <div class="step-item">
                    <strong>定时提醒：</strong>建议每小时记录一次，养成规律饮水习惯
                </div>
                <div class="step-item">
                    <strong>数据查看：</strong>定期查看日历数据，了解饮水规律
                </div>
            </div>

            <h3>个性化建议</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🎯 目标设定</h4>
                    <p>根据个人体重、活动量和气候调整每日目标。一般建议：体重(kg) × 30-40ml。</p>
                </div>
                <div class="feature-card">
                    <h4>⏰ 时间分配</h4>
                    <p>建议将每日目标分配到不同时段：早晨25%、上午25%、下午25%、晚上25%。</p>
                </div>
                <div class="feature-card">
                    <h4>📊 数据分析</h4>
                    <p>通过日历查看饮水规律，找出容易忘记饮水的时间段，有针对性地改善。</p>
                </div>
            </div>

            <h3>常用快捷操作</h3>
            <div class="code">
快速记录：点击浮动圆圈 或 点击弹窗中的"快速记录"按钮
快速撤回：弹窗中点击"快速撤回"按钮
查看历史：点击日历中的任意日期
清空记录：点击"删除当天记录"按钮（需确认）
调整设置：点击"设置"按钮进入配置页面
            </div>
        </div>

        <!-- 常见问题 -->
        <div class="section" id="faq">
            <h2>❓ 常见问题</h2>

            <h3>Q: 为什么拖拽浮动圆圈后没有记录饮水量？</h3>
            <p><strong>A:</strong> 这是正常的防误触机制。当拖拽时间超过0.5秒或拖拽距离超过5像素时，系统会认为您是在移动位置而不是记录饮水，因此不会触发记录。</p>

            <h3>Q: 如何快速记录饮水量？</h3>
            <p><strong>A:</strong> 快速点击浮动圆圈（按住时间少于0.5秒），或者打开弹窗点击"快速记录"按钮。</p>

            <h3>Q: 可以修改历史记录吗？</h3>
            <p><strong>A:</strong> 为了数据安全，历史记录是只读的。只能查看历史数据，不能修改。只有当天的记录可以进行添加、撤回和删除操作。</p>

            <h3>Q: 浮动圆圈消失了怎么办？</h3>
            <p><strong>A:</strong> 刷新页面即可恢复。如果问题持续，请检查插件是否正常启用，或尝试重新安装插件。</p>

            <h3>Q: 数据会丢失吗？</h3>
            <p><strong>A:</strong> 所有数据都保存在浏览器本地存储中，不会上传到服务器。建议定期备份重要数据，或开启Chrome同步功能。</p>

            <h3>Q: 如何设置提醒？</h3>
            <p><strong>A:</strong> 当前版本主要通过视觉提醒（浮动圆圈显示进度）。建议结合个人习惯，设置手机闹钟或其他提醒方式。</p>

            <h3>Q: 插件影响网页性能吗？</h3>
            <p><strong>A:</strong> WaterTime经过性能优化，对网页性能影响极小。如果遇到性能问题，可以在设置中关闭动画效果。</p>

            <div class="warning">
                <strong>⚠️ 注意事项：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>请适量饮水，避免过量导致水中毒</li>
                    <li>如有特殊疾病，请遵医嘱调整饮水量</li>
                    <li>插件仅作为辅助工具，不能替代专业医疗建议</li>
                </ul>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="section" style="text-align: center; background: rgba(0, 191, 255, 0.1);">
            <h2>🎉 感谢使用 WaterTime</h2>
            <p>希望WaterTime能帮助您养成健康的饮水习惯！</p>
            <p style="color: #666; margin-top: 20px;">
                如有问题或建议，欢迎反馈 | 版本：v2.0.0 | 更新日期：2025年1月
            </p>
            <p style="color: #00BFFF; margin-top: 15px; font-weight: 500;">
                由 @Renais 制作
            </p>
        </div>
    </div>
</body>
</html>
