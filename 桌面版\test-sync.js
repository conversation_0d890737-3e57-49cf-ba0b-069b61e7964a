// 测试数据同步的脚本
// 在浏览器控制台中运行

console.log('=== 数据同步测试开始 ===');

// 测试函数：检查主窗口和浮窗的数据是否一致
async function testDataSync() {
    try {
        console.log('=== 数据同步状态检查 ===');

        // 1. 检查主窗口数据
        if (window.app && window.app.data) {
            console.log('✅ 主窗口数据:', {
                todayTotal: window.app.data.todayTotal,
                dailyTarget: window.app.data.dailyTarget,
                recordsCount: window.app.data.todayRecords?.length || 0
            });
        } else {
            console.log('❌ 主窗口数据不可用');
        }

        // 2. 检查浮窗数据（如果存在）
        if (window.floatWidget && window.floatWidget.data) {
            console.log('✅ 浮窗数据:', {
                todayTotal: window.floatWidget.data.todayTotal,
                dailyTarget: window.floatWidget.data.dailyTarget
            });
        } else {
            console.log('❌ 浮窗数据不可用');
        }

        // 3. 直接检查存储数据
        if (window.electronAPI) {
            const todayKey = 'watertime_2025_7_24';
            const storageData = await window.electronAPI.getStoreData(todayKey);
            console.log('💾 存储数据:', storageData);

            const settings = await window.electronAPI.getStoreData('settings');
            console.log('⚙️ 设置数据:', settings);
        }

        // 4. 检查数据管理器
        if (window.dataManager) {
            const todayData = await window.dataManager.getTodayData();
            const settings = await window.dataManager.getSettings();
            console.log('🔧 数据管理器数据:', {
                todayData,
                settings
            });
        }

        // 5. 数据一致性检查
        const mainTotal = window.app?.data?.todayTotal || 0;
        const floatTotal = window.floatWidget?.data?.todayTotal || 0;

        if (mainTotal === floatTotal) {
            console.log('✅ 数据同步正常！主窗口和浮窗数据一致');
        } else {
            console.log('❌ 数据同步异常！主窗口:', mainTotal, 'ml, 浮窗:', floatTotal, 'ml');
        }

    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 添加饮水记录的测试函数
async function testAddDrink(amount = 200) {
    try {
        console.log(`🧪 === 测试添加 ${amount}ml 饮水记录 ===`);

        // 记录添加前的数据
        console.log('📊 添加前的数据状态:');
        await testDataSync();

        // 添加饮水记录
        if (window.app && window.app.addDrinkRecord) {
            await window.app.addDrinkRecord();
            console.log('✅ 通过主窗口添加饮水记录完成');
        } else if (window.dataManager) {
            await window.dataManager.addDrinkRecord(amount);
            console.log('✅ 通过数据管理器添加饮水记录完成');
        } else {
            console.log('❌ 无法添加饮水记录');
            return;
        }

        // 等待一下让数据同步
        console.log('⏳ 等待数据同步...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 记录添加后的数据
        console.log('📊 添加后的数据状态:');
        await testDataSync();

    } catch (error) {
        console.error('❌ 添加饮水记录测试失败:', error);
    }
}

// 定时检查数据同步状态
function startSyncMonitor() {
    console.log('开始监控数据同步状态...');
    setInterval(async () => {
        console.log('=== 定时检查数据同步 ===');
        await testDataSync();
    }, 5000); // 每5秒检查一次
}

// 导出测试函数
window.testDataSync = testDataSync;
window.testAddDrink = testAddDrink;
window.startSyncMonitor = startSyncMonitor;

console.log('测试脚本加载完成！');
console.log('可用命令:');
console.log('- testDataSync(): 检查数据同步状态');
console.log('- testAddDrink(amount): 测试添加饮水记录');
console.log('- startSyncMonitor(): 开始定时监控');
