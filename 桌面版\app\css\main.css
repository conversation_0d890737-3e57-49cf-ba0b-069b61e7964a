/* WaterTime 桌面版主窗口样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #E0F6FF 0%, #B0E0E6 50%, #87CEEB 100%);
    color: #2F4F4F;
    overflow: hidden;
    user-select: none;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}



/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 16px;  /* 减少内边距以节省空间 */
    overflow-y: auto;  /* 恢复垂直滚动条 */
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 16px;  /* 减少间距以节省空间 */
}

/* 今日概览 */
.today-overview {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-card {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.overview-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.overview-label {
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.overview-value {
    text-align: right;
}

.percentage {
    font-size: 24px;
    font-weight: bold;
    color: #00BFFF;
    display: block;
    line-height: 1;
}

.amount {
    font-size: 14px;
    color: #5F9EA0;
    margin-top: 2px;
}

.overview-remaining {
    text-align: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 14px;
    color: #5F9EA0;
}

/* 顶部快速操作 */
.top-actions {
    display: flex;
    gap: 12px;
}

/* 快速设置 */
.quick-settings {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.setting-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-item label {
    font-size: 13px;
    font-weight: 500;
    color: #5F9EA0;
    margin-bottom: 4px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 10px 35px 10px 12px;
    border: 1px solid rgba(95, 158, 160, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #5F9EA0;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #5F9EA0;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 3px rgba(95, 158, 160, 0.1);
}

.input-unit {
    position: absolute;
    right: 12px;
    font-size: 12px;
    color: rgba(95, 158, 160, 0.7);
    font-weight: 500;
    pointer-events: none;
}

/* 底部操作 */
.bottom-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

/* 统一的操作按钮样式 */
.action-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn.primary {
    background: rgba(0, 191, 255, 0.2);
    color: #00BFFF;
}

.action-btn.primary:hover {
    background: rgba(0, 191, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.3);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #5F9EA0;
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn.danger {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.action-btn.danger:hover {
    background: rgba(239, 68, 68, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn svg {
    width: 16px;
    height: 16px;
}

/* 饮水日历 */
.water-calendar {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.calendar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    border-radius: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #5F9EA0;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-bottom: 8px;
}

.weekday {
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #5F9EA0;
    padding: 8px 4px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-bottom: 16px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.1);
    color: #2F4F4F;
}

.calendar-day:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.calendar-day.other-month {
    color: #B0C4DE;
    opacity: 0.5;
}

.calendar-day.today {
    background: rgba(0, 191, 255, 0.3);
    color: #00BFFF;
    font-weight: 600;
    border: 2px solid #00BFFF;
}

.calendar-day.achieved {
    background: rgba(34, 197, 94, 0.8);
    color: white;
}

.calendar-day.achieved::after {
    content: '✓';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-day.partial {
    background: rgba(251, 191, 36, 0.8);
    color: white;
}

.calendar-day.partial::after {
    content: '◐';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-day.missed {
    background: rgba(239, 68, 68, 0.8);
    color: white;
}

.calendar-day.missed::after {
    content: '✗';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #5F9EA0;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-dot.achieved {
    background: rgba(34, 197, 94, 0.8);
}

.legend-dot.partial {
    background: rgba(251, 191, 36, 0.8);
}

.legend-dot.missed {
    background: rgba(239, 68, 68, 0.8);
}

/* 今日记录 */
.today-records {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 16px;  /* 减少内边距以节省空间 */
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.records-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.records-count {
    font-size: 12px;
    color: #5F9EA0;
    background: rgba(255, 255, 255, 0.3);
    padding: 4px 8px;
    border-radius: 12px;
}

.records-list {
    /* 完全移除滚动条和高度限制 */
    overflow: visible;
}

.record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;  /* 减少内边距 */
    margin-bottom: 6px;  /* 减少下边距 */
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.record-item:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateX(4px);
}

.record-item:last-child {
    margin-bottom: 0;
}

.record-time {
    font-size: 14px;
    color: #5F9EA0;
    font-weight: 500;
}

.record-amount {
    font-size: 14px;
    color: #00BFFF;
    font-weight: 600;
}

.record-delete {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0;
    transition: all 0.2s ease;
}

.record-item:hover .record-delete {
    opacity: 1;
}

.record-delete:hover {
    background: rgba(239, 68, 68, 0.1);
}

.records-empty {
    text-align: center;
    padding: 40px 20px;
    color: #B0C4DE;
    font-size: 14px;
}

.records-empty svg {
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
}


/* 底部信息 */
.footer {
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.app-info {
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
