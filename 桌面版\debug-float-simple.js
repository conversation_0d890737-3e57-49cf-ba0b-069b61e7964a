// 简单的浮窗调试脚本
console.log('=== 浮窗调试开始 ===');

// 1. 检查基本DOM元素
function checkDOM() {
    console.log('1. 检查DOM元素:');
    
    const container = document.getElementById('floatContainer');
    const widget = document.getElementById('floatWidget');
    
    console.log('floatContainer:', container ? '✅ 存在' : '❌ 不存在');
    console.log('floatWidget:', widget ? '✅ 存在' : '❌ 不存在');
    
    if (widget) {
        console.log('widget样式:', {
            display: getComputedStyle(widget).display,
            visibility: getComputedStyle(widget).visibility,
            opacity: getComputedStyle(widget).opacity,
            position: getComputedStyle(widget).position
        });
    }
}

// 2. 检查JavaScript对象
function checkJS() {
    console.log('2. 检查JavaScript对象:');
    
    console.log('window.floatWidget:', window.floatWidget ? '✅ 存在' : '❌ 不存在');
    console.log('window.dataManager:', window.dataManager ? '✅ 存在' : '❌ 不存在');
    console.log('window.electronAPI:', window.electronAPI ? '✅ 存在' : '❌ 不存在');
    
    if (window.floatWidget) {
        console.log('floatWidget数据:', window.floatWidget.data);
        console.log('floatWidget状态:', {
            isDragging: window.floatWidget.isDragging,
            isProcessing: window.floatWidget.isProcessing
        });
    }
}

// 3. 检查事件监听器
function checkEvents() {
    console.log('3. 检查事件监听器:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        // 手动触发点击事件测试
        console.log('手动触发点击事件...');
        widget.click();
    }
}

// 4. 检查CSS动画
function checkAnimations() {
    console.log('4. 检查CSS动画:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        // 手动添加悬浮动画
        widget.style.transform = 'scale(1.1)';
        widget.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            widget.style.transform = 'scale(1)';
        }, 1000);
        
        console.log('手动动画测试完成');
    }
}

// 5. 检查数据管理器
async function checkDataManager() {
    console.log('5. 检查数据管理器:');
    
    if (window.dataManager) {
        try {
            const settings = await window.dataManager.getSettings();
            const todayData = await window.dataManager.getTodayData();
            
            console.log('设置数据:', settings);
            console.log('今日数据:', todayData);
        } catch (error) {
            console.error('数据管理器错误:', error);
        }
    }
}

// 运行所有检查
async function runAllChecks() {
    checkDOM();
    checkJS();
    checkEvents();
    checkAnimations();
    await checkDataManager();
    
    console.log('=== 浮窗调试完成 ===');
}

// 导出函数
window.debugFloat = {
    checkDOM,
    checkJS,
    checkEvents,
    checkAnimations,
    checkDataManager,
    runAllChecks
};

console.log('调试脚本加载完成！运行 debugFloat.runAllChecks() 开始调试');
