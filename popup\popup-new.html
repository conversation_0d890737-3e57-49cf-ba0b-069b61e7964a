<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime - 饮水详情</title>
    <link rel="stylesheet" href="popup-new.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 标题区域 -->
        <div class="header">
            <h1 class="title">WaterTime 详情</h1>
            <p class="subtitle">今日饮水记录</p>
        </div>
        
        <!-- 今日概览 -->
        <div class="today-overview">
            <div class="overview-card">
                <div class="overview-main">
                    <div class="overview-label">今日进度</div>
                    <div class="overview-value">
                        <span class="percentage">0%</span>
                        <span class="amount">0ml / <span id="targetAmount">2000ml</span></span>
                    </div>
                </div>
                <div class="overview-remaining">
                    <span>还需 <span id="remainingAmount">2000ml</span></span>
                </div>
            </div>
        </div>

        <!-- 快速操作 - 移到日历上方 -->
        <div class="top-actions">
            <button id="quickDrink" class="action-btn primary">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z"/>
                </svg>
                <span>快速记录</span>
                <span id="quickAmount">200ml</span>
            </button>
            <button id="quickUndo" class="action-btn secondary">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.5,8C9.85,8 7.45,9 5.6,10.6L2,7V16H11L7.38,12.38C8.77,11.22 10.54,10.5 12.5,10.5C16.04,10.5 19.05,12.81 20.1,16L22.47,15.22C21.08,11.03 17.15,8 12.5,8Z"/>
                </svg>
                <span>快速撤回</span>
            </button>
        </div>

        <!-- 饮水日历 -->
        <div class="water-calendar">
            <div class="calendar-header">
                <button id="prevMonth" class="nav-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                </button>
                <h3 id="currentMonth">2024年1月</h3>
                <button id="nextMonth" class="nav-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                </button>
            </div>
            <div class="calendar-weekdays">
                <div class="weekday">日</div>
                <div class="weekday">一</div>
                <div class="weekday">二</div>
                <div class="weekday">三</div>
                <div class="weekday">四</div>
                <div class="weekday">五</div>
                <div class="weekday">六</div>
            </div>
            <div class="calendar-days" id="calendarDays">
                <!-- 日期将通过JavaScript动态生成 -->
            </div>
            <div class="calendar-legend">
                <div class="legend-item">
                    <div class="legend-dot achieved"></div>
                    <span>已达标</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot partial"></div>
                    <span>部分完成</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot missed"></div>
                    <span>未达标</span>
                </div>
            </div>
        </div>

        <!-- 底部操作 -->
        <div class="bottom-actions">
            <button id="clearToday" class="action-btn danger">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                </svg>
                <span>删除当天记录</span>
            </button>
            <button id="settingsToggle" class="action-btn secondary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                </svg>
                <span>设置</span>
            </button>
        </div>

        <!-- 设置面板 -->
        <div class="settings-panel" id="settingsPanel">
            <div class="settings-header">
                <h3>设置</h3>
                <button id="closeSettings" class="close-btn">×</button>
            </div>

            <div class="settings-content">
                <!-- 基础设置 -->
                <div class="setting-group">
                    <h4>基础设置</h4>
                    <div class="setting-item">
                        <label>每日目标</label>
                        <div class="input-group">
                            <input type="number" id="dailyTargetInput" min="500" max="10000" step="100" value="2000">
                            <span class="unit">ml</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label>单次饮水量</label>
                        <div class="input-group">
                            <input type="number" id="singleDrinkInput" min="50" max="2000" step="50" value="200">
                            <span class="unit">ml</span>
                        </div>
                    </div>
                </div>

                <!-- 显示设置 -->
                <div class="setting-group">
                    <h4>显示设置</h4>
                    <div class="setting-item">
                        <label>浮动位置</label>
                        <select id="floatPositionSelect">
                            <option value="bottom-right">右下角</option>
                            <option value="bottom-left">左下角</option>
                            <option value="top-right">右上角</option>
                            <option value="top-left">左上角</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>圆圈大小</label>
                        <select id="circleSizeSelect">
                            <option value="small">小 (100px)</option>
                            <option value="medium">中 (120px)</option>
                            <option value="large">大 (140px)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>启用动画</label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="enableAnimationsToggle">
                            <label for="enableAnimationsToggle" class="toggle-label">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="settings-actions">
                    <button id="saveSettings" class="btn primary">保存设置</button>
                    <button id="resetSettings" class="btn secondary">重置</button>
                </div>
            </div>
        </div>
        
        <!-- 提示信息 -->
        <div class="tip">
            <p>💡 浮动圆圈已显示在页面右下角，点击即可快速记录饮水</p>
        </div>

        <!-- 制作者信息 -->
        <div class="author-info">
            <span>由 @Renais 制作</span>
        </div>
    </div>

    <script src="popup-new.js"></script>
</body>
</html>
