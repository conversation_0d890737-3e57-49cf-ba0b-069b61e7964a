// WaterTime 弹窗交互逻辑

// DOM元素引用
const waterButton = document.getElementById('waterButton');
const settingsButton = document.getElementById('settingsButton');
const progressArc = document.querySelector('.progress-arc');
const percentageElement = document.querySelector('.percentage');
const amountElement = document.querySelector('.amount');
const targetAmountElement = document.getElementById('targetAmount');
const currentAmountElement = document.getElementById('currentAmount');
const remainingAmountElement = document.getElementById('remainingAmount');

// 应用状态
let currentData = {
    dailyTarget: 2000,      // 每日目标饮水量(ml)
    singleDrink: 200,       // 单次饮水量(ml)
    todayTotal: 0,          // 今日已饮水量(ml)
    todayRecords: []        // 今日饮水记录时间
};

// 应用状态管理
let appState = {
    isProcessing: false,    // 防止重复点击
    lastClickTime: 0,       // 上次点击时间
    clickCount: 0,          // 今日点击次数
    isInitialized: false    // 是否已初始化
};

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    console.log('WaterTime 弹窗初始化...');
    
    // 加载用户配置和今日数据
    await loadUserData();
    
    // 更新界面显示
    updateDisplay();
    
    // 绑定事件监听器
    bindEventListeners();
    
    console.log('WaterTime 弹窗初始化完成');
});

// 加载用户数据（增强版）
async function loadUserData() {
    try {
        // 检查Chrome Storage API是否可用
        if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
            console.warn('Chrome Storage API 不可用，使用默认值');
            await loadLocalStorageData(); // 降级到localStorage
            return;
        }

        // 获取用户配置
        const configResult = await chrome.storage.local.get(['dailyTarget', 'singleDrink']);

        // 验证和设置配置数据
        if (configResult.dailyTarget && isValidTarget(configResult.dailyTarget)) {
            currentData.dailyTarget = configResult.dailyTarget;
        } else {
            console.warn('无效的每日目标，使用默认值');
        }

        if (configResult.singleDrink && isValidSingleDrink(configResult.singleDrink)) {
            currentData.singleDrink = configResult.singleDrink;
        } else {
            console.warn('无效的单次饮水量，使用默认值');
        }

        // 获取今日饮水记录
        const today = getTodayKey();
        const todayResult = await chrome.storage.local.get([today]);

        if (todayResult[today] && isValidTodayData(todayResult[today])) {
            currentData.todayTotal = todayResult[today].total || 0;
            currentData.todayRecords = todayResult[today].records || [];
            appState.clickCount = currentData.todayRecords.length;
        } else {
            console.warn('今日数据无效或不存在，重置为默认值');
            await resetTodayData();
        }

        // 检查是否需要重置（跨日期）
        await checkAndResetIfNewDay();

        console.log('用户数据加载完成:', currentData);
        appState.isInitialized = true;

    } catch (error) {
        console.error('加载用户数据失败:', error);
        await handleDataLoadError();
    }
}

// 数据验证函数
function isValidTarget(target) {
    return typeof target === 'number' && target >= 500 && target <= 10000;
}

function isValidSingleDrink(amount) {
    return typeof amount === 'number' && amount >= 50 && amount <= 2000;
}

function isValidTodayData(data) {
    return data &&
           typeof data.total === 'number' &&
           data.total >= 0 &&
           Array.isArray(data.records);
}

// 降级到localStorage
async function loadLocalStorageData() {
    try {
        const savedData = localStorage.getItem('watertime_data');
        if (savedData) {
            const data = JSON.parse(savedData);
            if (data.dailyTarget) currentData.dailyTarget = data.dailyTarget;
            if (data.singleDrink) currentData.singleDrink = data.singleDrink;
            console.log('从localStorage加载数据');
        }
    } catch (error) {
        console.error('localStorage加载失败:', error);
    }
}

// 处理数据加载错误
async function handleDataLoadError() {
    console.log('使用默认配置');
    currentData = {
        dailyTarget: 2000,
        singleDrink: 200,
        todayTotal: 0,
        todayRecords: []
    };
    appState.isInitialized = true;
}

// 获取今日日期键
function getTodayKey() {
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
}

// 获取当前时间字符串
function getCurrentTimeString() {
    const now = new Date();
    return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
}

// 更新界面显示
function updateDisplay() {
    // 计算进度百分比
    const percentage = Math.min(Math.round((currentData.todayTotal / currentData.dailyTarget) * 100), 100);
    const remaining = Math.max(currentData.dailyTarget - currentData.todayTotal, 0);

    // 更新进度圈
    updateProgressCircle(percentage);

    // 更新文字显示
    percentageElement.textContent = `${percentage}%`;
    amountElement.textContent = `${currentData.todayTotal}ml`;

    // 更新底部统计
    targetAmountElement.textContent = `${currentData.dailyTarget}ml`;
    currentAmountElement.textContent = `${currentData.todayTotal}ml`;
    remainingAmountElement.textContent = `${remaining}ml`;

    console.log(`界面更新: ${percentage}% (${currentData.todayTotal}/${currentData.dailyTarget}ml)`);
}

// 带动画的界面更新
async function updateDisplayWithAnimation(oldPercentage, newPercentage) {
    return new Promise((resolve) => {
        // 数字动画更新
        animateNumber(percentageElement, oldPercentage, newPercentage, '%', 600);
        animateNumber(amountElement, currentData.todayTotal - currentData.singleDrink, currentData.todayTotal, 'ml', 600);

        // 计算剩余量
        const remaining = Math.max(currentData.dailyTarget - currentData.todayTotal, 0);
        const oldRemaining = remaining + currentData.singleDrink;
        animateNumber(remainingAmountElement, oldRemaining, remaining, 'ml', 600);

        // 更新进度圈（已有动画）
        updateProgressCircle(newPercentage);

        // 更新底部当前饮水量
        currentAmountElement.textContent = `${currentData.todayTotal}ml`;

        setTimeout(resolve, 600);
    });
}

// 数字动画函数
function animateNumber(element, startValue, endValue, suffix = '', duration = 600) {
    const startTime = performance.now();
    const difference = endValue - startValue;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.round(startValue + (difference * easeOutQuart));

        element.textContent = `${currentValue}${suffix}`;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 更新进度圈
function updateProgressCircle(percentage) {
    const circumference = 2 * Math.PI * 80; // 半径80的圆周长
    const offset = circumference - (percentage / 100) * circumference;

    progressArc.style.strokeDashoffset = offset;

    // 根据进度改变颜色和效果
    if (percentage >= 100) {
        progressArc.style.stroke = '#32CD32'; // 完成时显示绿色
        progressArc.classList.add('glowing');
    } else if (percentage >= 75) {
        progressArc.style.stroke = '#00BFFF'; // 75%以上显示蓝色
        progressArc.classList.add('glowing');
    } else if (percentage >= 50) {
        progressArc.style.stroke = '#87CEEB'; // 50%以上显示天青色
        progressArc.classList.remove('glowing');
    } else {
        progressArc.style.stroke = '#B0E0E6'; // 50%以下显示浅蓝色
        progressArc.classList.remove('glowing');
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 水杯按钮点击事件
    waterButton.addEventListener('click', handleWaterButtonClick);
    
    // 设置按钮点击事件
    settingsButton.addEventListener('click', handleSettingsButtonClick);
    
    // 键盘快捷键支持
    document.addEventListener('keydown', (event) => {
        if (event.code === 'Space' || event.code === 'Enter') {
            event.preventDefault();
            handleWaterButtonClick();
        }
    });
}

// 处理饮水按钮点击（增强版）
async function handleWaterButtonClick() {
    try {
        // 防抖处理：防止快速重复点击
        const now = Date.now();
        if (appState.isProcessing || (now - appState.lastClickTime) < 500) {
            console.log('点击过于频繁，忽略此次点击');
            return;
        }

        appState.isProcessing = true;
        appState.lastClickTime = now;
        appState.clickCount++;

        console.log(`记录饮水: ${currentData.singleDrink}ml (第${appState.clickCount}次)`);

        // 添加点击动画效果
        waterButton.classList.add('clicked');

        // 禁用按钮防止重复点击
        waterButton.disabled = true;
        waterButton.style.pointerEvents = 'none';

        // 更新数据
        const oldTotal = currentData.todayTotal;
        const oldPercentage = Math.round((oldTotal / currentData.dailyTarget) * 100);

        currentData.todayTotal += currentData.singleDrink;
        currentData.todayRecords.push(getCurrentTimeString());

        const newPercentage = Math.round((currentData.todayTotal / currentData.dailyTarget) * 100);

        // 保存到存储
        await saveTodayData();

        // 更新界面（带动画）
        await updateDisplayWithAnimation(oldPercentage, newPercentage);

        // 移除点击动画
        setTimeout(() => {
            waterButton.classList.remove('clicked');
        }, 800);

        // 添加成功反馈动画
        setTimeout(() => {
            waterButton.classList.add('success');
            setTimeout(() => {
                waterButton.classList.remove('success');
            }, 1000);
        }, 400);

        // 检查是否达成目标
        if (oldTotal < currentData.dailyTarget && currentData.todayTotal >= currentData.dailyTarget) {
            setTimeout(() => showCelebration(), 600);
        }

        // 重新启用按钮
        setTimeout(() => {
            waterButton.disabled = false;
            waterButton.style.pointerEvents = 'auto';
            appState.isProcessing = false;
        }, 1000);

    } catch (error) {
        console.error('记录饮水失败:', error);
        showErrorFeedback('记录失败，请重试');

        // 错误时也要重新启用按钮
        waterButton.disabled = false;
        waterButton.style.pointerEvents = 'auto';
        appState.isProcessing = false;
    }
}

// 保存今日数据
async function saveTodayData() {
    try {
        // 检查Chrome Storage API是否可用
        if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
            console.warn('Chrome Storage API 不可用，无法保存数据');
            return;
        }

        const today = getTodayKey();
        const todayData = {
            total: currentData.todayTotal,
            records: currentData.todayRecords
        };

        await chrome.storage.local.set({
            [today]: todayData
        });

        console.log('今日数据已保存:', todayData);
    } catch (error) {
        console.error('保存今日数据失败:', error);
    }
}

// 处理设置按钮点击
function handleSettingsButtonClick() {
    // 打开设置页面
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.openOptionsPage) {
        chrome.runtime.openOptionsPage();
    } else {
        console.warn('无法打开设置页面：Chrome API 不可用');
        alert('设置功能暂时不可用');
    }
}

// 显示庆祝动画（达成目标时）
function showCelebration() {
    // 简单的庆祝效果
    document.body.style.animation = 'celebration 1s ease-in-out';
    
    setTimeout(() => {
        document.body.style.animation = '';
    }, 1000);
    
    console.log('🎉 恭喜！今日饮水目标达成！');
}

// 添加庆祝动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes celebration {
        0%, 100% { transform: scale(1); }
        25% { transform: scale(1.02); }
        50% { transform: scale(1.05); }
        75% { transform: scale(1.02); }
    }
`;
document.head.appendChild(style);

// 监听存储变化（当设置页面更新配置时）
if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.onChanged) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
            let needUpdate = false;

            if (changes.dailyTarget) {
                currentData.dailyTarget = changes.dailyTarget.newValue;
                needUpdate = true;
            }

            if (changes.singleDrink) {
                currentData.singleDrink = changes.singleDrink.newValue;
                needUpdate = true;
            }

            if (needUpdate) {
                updateDisplay();
                console.log('配置已更新:', currentData);
            }
        }
    });
}
