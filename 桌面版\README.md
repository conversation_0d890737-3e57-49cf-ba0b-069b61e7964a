# WaterTime 桌面版

一个简洁美观的桌面饮水记录应用，帮助您养成良好的饮水习惯。

## 功能特性

- 🎯 **智能提醒** - 定时提醒您饮水
- 📊 **进度追踪** - 实时显示每日饮水进度
- 🎨 **美观界面** - 现代化的用户界面设计
- 🔄 **浮动圆圈** - 桌面浮动进度显示
- 📅 **历史记录** - 查看历史饮水数据
- ⚙️ **个性化设置** - 自定义目标和提醒

## 系统要求

- Windows 10 或更高版本
- 至少 100MB 可用磁盘空间
- 1GB RAM

## 安装说明

1. 下载最新版本的安装包
2. 双击运行安装程序
3. 按照向导完成安装
4. 启动 WaterTime 开始使用

## 使用方法

### 基本操作
- **快速记录**: 点击"快速记录"按钮或浮动圆圈
- **查看进度**: 在主界面查看今日饮水进度
- **历史查看**: 通过日历查看历史记录
- **设置调整**: 在设置区域调整目标和偏好

### 浮动圆圈
- **拖拽移动**: 按住浮动圆圈可以拖拽到任意位置
- **单击记录**: 单击圆圈快速记录饮水
- **双击打开**: 双击圆圈打开主窗口

### 系统托盘
- **左键点击**: 显示/隐藏主窗口
- **右键菜单**: 访问设置和退出选项

## 开发说明

### 技术栈
- **框架**: Electron
- **前端**: HTML5 + CSS3 + JavaScript
- **数据存储**: electron-store
- **打包工具**: electron-builder

### 开发环境
```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 构建应用
npm run build

# 打包 Windows 版本
npm run build-win
```

### 项目结构
```
桌面版/
├── main.js              # 主进程
├── preload.js           # 预加载脚本
├── package.json         # 项目配置
├── app/                 # 应用主体
│   ├── index.html       # 主窗口
│   ├── float.html       # 浮动窗口
│   ├── css/            # 样式文件
│   ├── js/             # JavaScript 文件
│   └── assets/         # 资源文件
├── build/              # 构建配置
└── dist/               # 打包输出
```

## 版本历史

### v1.0.0 (开发中)
- ✅ 基础项目结构搭建
- ✅ 主窗口界面实现
- ✅ 浮动圆圈功能
- ✅ 系统托盘集成
- 🔄 数据存储和同步
- 🔄 日历功能实现
- 🔄 设置功能完善

## 许可证

MIT License

## 联系我们

如有问题或建议，请联系开发团队。

---

**WaterTime Desktop** - 让饮水成为一种习惯 💧
