<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .data-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WaterTime 数据同步测试</h1>
        
        <div id="status" class="status info">
            准备测试...
        </div>
        
        <div class="controls">
            <button onclick="testDataAccess()">测试数据访问</button>
            <button onclick="testSync()">测试数据同步</button>
            <button onclick="forceSync()">强制同步</button>
            <button onclick="testFloat()">测试浮窗</button>
            <button onclick="clearCache()">清除缓存</button>
        </div>
        
        <div id="dataDisplay" class="data-display">
            等待测试结果...
        </div>
        
        <div id="floatTest" class="data-display" style="display: none;">
            <h3>浮窗测试代码</h3>
            <p>请将以下代码复制到浮窗开发者工具中运行：</p>
            <textarea id="floatCode" style="width: 100%; height: 200px; font-family: monospace;"></textarea>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateDisplay(data) {
            document.getElementById('dataDisplay').innerHTML = 
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }

        async function testDataAccess() {
            updateStatus('测试数据访问...', 'info');
            
            try {
                if (!window.electronAPI) {
                    throw new Error('electronAPI 不可用');
                }

                const todayKey = 'watertime_2025_7_24';
                const [settings, todayData] = await Promise.all([
                    window.electronAPI.getStoreData('settings'),
                    window.electronAPI.getStoreData(todayKey)
                ]);

                const result = {
                    settings: settings,
                    todayData: todayData,
                    todayKey: todayKey,
                    timestamp: new Date().toISOString()
                };

                updateDisplay(result);
                updateStatus('数据访问成功', 'success');
                
                return result;
            } catch (error) {
                updateStatus('数据访问失败: ' + error.message, 'error');
                updateDisplay({ error: error.message });
                return null;
            }
        }

        async function testSync() {
            updateStatus('测试数据同步...', 'info');
            
            try {
                const data = await testDataAccess();
                if (!data) return;

                // 触发数据更新
                const todayKey = 'watertime_2025_7_24';
                const updatedData = {
                    ...data.todayData,
                    updated: new Date().toISOString(),
                    syncTest: true
                };

                await window.electronAPI.setStoreData(todayKey, updatedData);
                
                updateStatus('数据同步信号已发送', 'success');
                
                // 等待一秒后重新检查
                setTimeout(async () => {
                    const newData = await window.electronAPI.getStoreData(todayKey);
                    updateDisplay({
                        message: '同步后的数据',
                        data: newData
                    });
                }, 1000);
                
            } catch (error) {
                updateStatus('同步测试失败: ' + error.message, 'error');
            }
        }

        async function forceSync() {
            updateStatus('强制同步数据...', 'info');
            
            try {
                const todayKey = 'watertime_2025_7_24';
                const data = await window.electronAPI.getStoreData(todayKey);
                
                if (data) {
                    // 强制触发数据更新
                    await window.electronAPI.setStoreData(todayKey, {
                        ...data,
                        forceSync: Date.now(),
                        updated: new Date().toISOString()
                    });
                    
                    updateStatus(`强制同步完成 - 总量: ${data.total}ml`, 'success');
                    updateDisplay({
                        action: '强制同步',
                        total: data.total,
                        records: data.records?.length || 0,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    updateStatus('没有找到数据', 'error');
                }
            } catch (error) {
                updateStatus('强制同步失败: ' + error.message, 'error');
            }
        }

        function testFloat() {
            const floatCode = `
// === 浮窗修复测试代码 ===
console.log('=== 开始浮窗修复测试 ===');

// 1. 检查基本组件
console.log('1. 检查组件:');
console.log('dataManager:', window.dataManager ? '✓' : '✗');
console.log('floatWidget:', window.floatWidget ? '✓' : '✗');
console.log('electronAPI:', window.electronAPI ? '✓' : '✗');

// 2. 直接获取数据
async function directDataFix() {
    try {
        const todayKey = 'watertime_2025_7_24';
        const data = await window.electronAPI.getStoreData(todayKey);
        console.log('直接获取的数据:', data);
        
        if (data && window.floatWidget) {
            // 直接设置数据
            window.floatWidget.data.todayTotal = data.total || 0;
            window.floatWidget.data.dailyTarget = 2000;
            
            // 强制更新显示
            window.floatWidget.updateProgress();
            
            console.log('数据已强制更新:', window.floatWidget.data);
            
            // 更新DOM
            const percentage = Math.min(100, Math.round((data.total / 2000) * 100));
            document.getElementById('floatPercentage').textContent = percentage + '%';
            document.getElementById('floatAmount').textContent = data.total + 'ml';
            
            console.log('DOM已更新:', percentage + '%', data.total + 'ml');
        }
    } catch (error) {
        console.error('直接修复失败:', error);
    }
}

// 3. 测试点击
function testClick() {
    const widget = document.getElementById('floatWidget');
    if (widget) {
        console.log('添加测试点击监听器...');
        widget.addEventListener('click', () => {
            console.log('点击测试成功!');
        });
        console.log('请点击浮窗测试');
    }
}

// 执行修复
directDataFix();
testClick();

console.log('=== 浮窗修复测试完成 ===');
            `;
            
            document.getElementById('floatCode').value = floatCode;
            document.getElementById('floatTest').style.display = 'block';
            updateStatus('浮窗测试代码已生成，请复制到浮窗控制台运行', 'info');
        }

        async function clearCache() {
            updateStatus('清除缓存...', 'info');
            
            try {
                if (window.dataManager && window.dataManager.cache) {
                    window.dataManager.cache.clear();
                    updateStatus('缓存已清除', 'success');
                } else {
                    updateStatus('数据管理器不可用', 'error');
                }
            } catch (error) {
                updateStatus('清除缓存失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            updateStatus('页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
