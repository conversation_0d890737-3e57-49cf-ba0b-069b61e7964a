# 图标生成说明

## 需要的图标尺寸
- icon16.png (16x16像素)
- icon48.png (48x48像素)  
- icon128.png (128x128像素)

## 生成方法

### 方法1：在线转换（推荐）
1. 打开网站：https://convertio.co/zh/svg-png/
2. 上传 `icon.svg` 文件
3. 分别设置输出尺寸为 16x16、48x48、128x128
4. 下载生成的PNG文件
5. 重命名为 icon16.png、icon48.png、icon128.png

### 方法2：使用Photoshop/GIMP
1. 打开 `icon.svg` 文件
2. 设置画布尺寸为所需大小
3. 导出为PNG格式

### 方法3：使用VS Code插件
1. 安装 "SVG" 插件
2. 右键点击 `icon.svg`
3. 选择 "Export PNG" 并设置尺寸

## 临时解决方案
如果暂时无法生成PNG图标，可以先使用SVG文件进行开发测试，在manifest.json中暂时使用SVG路径。

## 注意事项
- 确保图标背景透明或使用圆形背景
- 保持图标在不同尺寸下的清晰度
- 主色调保持天青色系 (#87CEEB)
