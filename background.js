// WaterTime 扩展后台脚本
// 用于处理扩展的生命周期事件和数据管理

// 默认配置
const DEFAULT_SETTINGS = {
  dailyTarget: 2000,
  singleDrink: 200,
  floatPosition: 'bottom-right',
  circleSize: 'medium',
  enableAnimations: true
};

// 数据管理器
class WaterTimeDataManager {
  constructor() {
    this.isInitialized = false;
  }

  // 初始化数据管理器
  async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('WaterTime 数据管理器初始化...');

      // 检查并设置默认配置
      await this.ensureDefaultSettings();

      // 清理过期数据
      await this.cleanupOldData();

      // 设置每日重置定时器
      await this.setupDailyReset();

      this.isInitialized = true;
      console.log('WaterTime 数据管理器初始化完成');

    } catch (error) {
      console.error('数据管理器初始化失败:', error);
    }
  }

  // 确保默认设置存在
  async ensureDefaultSettings() {
    try {
      const existingSettings = await chrome.storage.local.get(Object.keys(DEFAULT_SETTINGS));
      const missingSettings = {};

      // 检查缺失的设置
      Object.keys(DEFAULT_SETTINGS).forEach(key => {
        if (existingSettings[key] === undefined) {
          missingSettings[key] = DEFAULT_SETTINGS[key];
        }
      });

      // 保存缺失的设置
      if (Object.keys(missingSettings).length > 0) {
        await chrome.storage.local.set(missingSettings);
        console.log('已设置默认配置:', missingSettings);
      }

    } catch (error) {
      console.error('设置默认配置失败:', error);
    }
  }

  // 清理过期数据（保留最近30天）
  async cleanupOldData() {
    try {
      const allData = await chrome.storage.local.get(null);
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const keysToRemove = [];

      Object.keys(allData).forEach(key => {
        if (key.startsWith('watertime_')) {
          // 解析日期
          const dateParts = key.replace('watertime_', '').split('_');
          if (dateParts.length === 3) {
            const recordDate = new Date(
              parseInt(dateParts[0]),
              parseInt(dateParts[1]) - 1,
              parseInt(dateParts[2])
            );

            if (recordDate < thirtyDaysAgo) {
              keysToRemove.push(key);
            }
          }
        }
      });

      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`清理了 ${keysToRemove.length} 条过期记录`);
      }

    } catch (error) {
      console.error('清理过期数据失败:', error);
    }
  }

  // 设置每日重置定时器
  async setupDailyReset() {
    try {
      // 清除现有的定时器
      await chrome.alarms.clear('dailyReset');

      // 计算下一个午夜的时间
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      // 设置定时器
      await chrome.alarms.create('dailyReset', {
        when: tomorrow.getTime(),
        periodInMinutes: 24 * 60 // 每24小时重复
      });

      console.log('每日重置定时器已设置');

    } catch (error) {
      console.error('设置每日重置定时器失败:', error);
    }
  }

  // 获取今日数据
  async getTodayData() {
    try {
      const today = this.getTodayKey();
      const result = await chrome.storage.local.get([today]);

      return result[today] || { total: 0, records: [] };
    } catch (error) {
      console.error('获取今日数据失败:', error);
      return { total: 0, records: [] };
    }
  }

  // 保存今日数据
  async saveTodayData(data) {
    try {
      const today = this.getTodayKey();
      await chrome.storage.local.set({ [today]: data });
      console.log('今日数据已保存:', data);
    } catch (error) {
      console.error('保存今日数据失败:', error);
      throw error;
    }
  }

  // 获取今日键名
  getTodayKey() {
    const today = new Date();
    return `watertime_${today.getFullYear()}_${today.getMonth() + 1}_${today.getDate()}`;
  }

  // 验证设置数据
  validateSettings(settings) {
    const errors = [];

    if (settings.dailyTarget !== undefined) {
      if (typeof settings.dailyTarget !== 'number' || settings.dailyTarget < 500 || settings.dailyTarget > 10000) {
        errors.push('每日目标必须在500-10000ml之间');
      }
    }

    if (settings.singleDrink !== undefined) {
      if (typeof settings.singleDrink !== 'number' || settings.singleDrink < 50 || settings.singleDrink > 2000) {
        errors.push('单次饮水量必须在50-2000ml之间');
      }
    }

    return errors;
  }
}

// 创建数据管理器实例
const dataManager = new WaterTimeDataManager();

// 检查Chrome API是否可用
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onInstalled) {
  // 扩展安装时的初始化
  chrome.runtime.onInstalled.addListener(async (details) => {
    console.log('WaterTime 扩展已安装/更新');

    // 初始化数据管理器
    await dataManager.initialize();

    // 如果是首次安装，显示欢迎信息
    if (details.reason === 'install') {
      console.log('欢迎使用 WaterTime！');
      // 可以在这里打开设置页面或显示欢迎页面
    }
  });
}

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(async () => {
  console.log('WaterTime 扩展启动');
  await dataManager.initialize();
});

// 处理每日数据重置
if (typeof chrome !== 'undefined' && chrome.alarms && chrome.alarms.onAlarm) {
  chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'dailyReset') {
      console.log('执行每日数据重置');

      try {
        // 清理过期数据
        await dataManager.cleanupOldData();

        // 重新设置下一次重置
        await dataManager.setupDailyReset();

        console.log('每日重置完成');
      } catch (error) {
        console.error('每日重置失败:', error);
      }
    }
  });
}

// 监听存储变化，进行数据验证
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    // 验证设置变化
    const settingsChanges = {};
    Object.keys(DEFAULT_SETTINGS).forEach(key => {
      if (changes[key]) {
        settingsChanges[key] = changes[key].newValue;
      }
    });

    if (Object.keys(settingsChanges).length > 0) {
      const errors = dataManager.validateSettings(settingsChanges);
      if (errors.length > 0) {
        console.warn('设置验证失败:', errors);
      } else {
        console.log('设置更新成功:', settingsChanges);
      }
    }
  }
});

// 导出数据管理器供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { dataManager, DEFAULT_SETTINGS };
}
