// WaterTime 详情页面脚本

// DOM元素引用
const percentageElement = document.querySelector('.percentage');
const amountElement = document.querySelector('.amount');
const targetAmountElement = document.getElementById('targetAmount');
const remainingAmountElement = document.getElementById('remainingAmount');

const quickDrinkButton = document.getElementById('quickDrink');
const quickUndoButton = document.getElementById('quickUndo');
const clearTodayButton = document.getElementById('clearToday');
const quickAmountElement = document.getElementById('quickAmount');
const settingsToggle = document.getElementById('settingsToggle');
const settingsPanel = document.getElementById('settingsPanel');
const closeSettings = document.getElementById('closeSettings');
const saveSettingsBtn = document.getElementById('saveSettings');
const resetSettingsBtn = document.getElementById('resetSettings');

// 日历相关元素
const currentMonthElement = document.getElementById('currentMonth');
const prevMonthBtn = document.getElementById('prevMonth');
const nextMonthBtn = document.getElementById('nextMonth');
const calendarDaysElement = document.getElementById('calendarDays');

// 应用数据
let watertimeData = {
    dailyTarget: 2000,
    singleDrink: 200,
    todayTotal: 0,
    // 显示设置
    floatPosition: 'bottom-right',
    circleSize: 'medium',
    enableAnimations: true
};

// 日历状态
let calendarState = {
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth(),
    selectedDate: null, // 当前选中的日期 {year, month, day}
    monthlyData: {} // 存储每月的饮水数据
};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('WaterTime 详情页面初始化...');

    await loadData();
    await loadCalendarData();
    updateDisplay();
    updateCalendar();
    bindEvents();
    initializeSettings();

    // 默认选择今天
    const today = new Date();
    calendarState.selectedDate = {
        year: today.getFullYear(),
        month: today.getMonth(),
        day: today.getDate()
    };

    // 启用今日操作
    enableTodayOperations();

    // 重新渲染日历以显示选中状态
    updateCalendar();

    console.log('WaterTime 详情页面初始化完成');
});

// 加载数据
async function loadData() {
    try {
        // 获取配置
        const configResult = await chrome.storage.local.get([
            'dailyTarget', 'singleDrink', 'floatPosition', 'circleSize', 'enableAnimations'
        ]);
        if (configResult.dailyTarget) {
            watertimeData.dailyTarget = configResult.dailyTarget;
        }
        if (configResult.singleDrink) {
            watertimeData.singleDrink = configResult.singleDrink;
        }
        if (configResult.floatPosition) {
            watertimeData.floatPosition = configResult.floatPosition;
        }
        if (configResult.circleSize) {
            watertimeData.circleSize = configResult.circleSize;
        }
        if (configResult.enableAnimations !== undefined) {
            watertimeData.enableAnimations = configResult.enableAnimations;
        }
        
        // 获取今日数据
        const today = getTodayKey();
        const todayResult = await chrome.storage.local.get([today]);
        if (todayResult[today]) {
            watertimeData.todayTotal = todayResult[today].total || 0;
        }
        
        console.log('数据加载完成:', watertimeData);
    } catch (error) {
        console.error('数据加载失败:', error);
    }
}

// 更新显示
function updateDisplay() {
    // 计算进度
    const percentage = Math.min(Math.round((watertimeData.todayTotal / watertimeData.dailyTarget) * 100), 100);
    const remaining = Math.max(watertimeData.dailyTarget - watertimeData.todayTotal, 0);
    
    // 更新概览
    percentageElement.textContent = `${percentage}%`;
    amountElement.innerHTML = `${watertimeData.todayTotal}ml / <span id="targetAmount">${watertimeData.dailyTarget}ml</span>`;
    targetAmountElement.textContent = `${watertimeData.dailyTarget}ml`;
    remainingAmountElement.textContent = `${remaining}ml`;
    
    // 更新快速操作
    quickAmountElement.textContent = `${watertimeData.singleDrink}ml`;
    
}

// 绑定事件
function bindEvents() {
    // 快速记录按钮
    quickDrinkButton.addEventListener('click', handleQuickDrink);

    // 快速撤回按钮
    quickUndoButton.addEventListener('click', handleQuickUndo);

    // 删除当天记录按钮
    clearTodayButton.addEventListener('click', handleClearToday);

    // 设置按钮
    settingsToggle.addEventListener('click', showSettings);
    closeSettings.addEventListener('click', hideSettings);
    saveSettingsBtn.addEventListener('click', saveSettings);
    resetSettingsBtn.addEventListener('click', resetSettings);

    // 日历事件
    prevMonthBtn.addEventListener('click', () => {
        calendarState.currentMonth--;
        if (calendarState.currentMonth < 0) {
            calendarState.currentMonth = 11;
            calendarState.currentYear--;
        }
        updateCalendar();
    });

    nextMonthBtn.addEventListener('click', () => {
        calendarState.currentMonth++;
        if (calendarState.currentMonth > 11) {
            calendarState.currentMonth = 0;
            calendarState.currentYear++;
        }
        updateCalendar();
    });

    // 监听存储变化
    chrome.storage.onChanged.addListener(handleStorageChange);
}

// 处理快速记录
async function handleQuickDrink() {
    try {
        // 检查是否选择的是今天
        if (!isSelectedDateToday()) {
            showErrorFeedback('只能在当天进行记录操作');
            return;
        }

        console.log(`快速记录饮水: ${watertimeData.singleDrink}ml`);

        // 添加点击效果
        quickDrinkButton.style.transform = 'scale(0.95)';
        setTimeout(() => {
            quickDrinkButton.style.transform = '';
        }, 150);

        // 更新数据
        watertimeData.todayTotal += watertimeData.singleDrink;
        
        // 保存数据
        await saveData();

        // 保存到日历
        await saveDayToCalendar();

        // 更新显示
        updateDisplay();
        
        // 成功反馈
        showSuccessFeedback();
        
    } catch (error) {
        console.error('快速记录失败:', error);
        showErrorFeedback();
    }
}

// 初始化设置面板
function initializeSettings() {
    // 设置表单值
    document.getElementById('dailyTargetInput').value = watertimeData.dailyTarget;
    document.getElementById('singleDrinkInput').value = watertimeData.singleDrink;
    document.getElementById('floatPositionSelect').value = watertimeData.floatPosition;
    document.getElementById('circleSizeSelect').value = watertimeData.circleSize;
    document.getElementById('enableAnimationsToggle').checked = watertimeData.enableAnimations;
}

// 显示设置面板
function showSettings() {
    settingsPanel.classList.add('show');
    // 更新设置值
    initializeSettings();
}

// 隐藏设置面板
function hideSettings() {
    settingsPanel.classList.remove('show');
}

// 保存设置
async function saveSettings() {
    try {
        // 获取表单值
        const dailyTarget = parseInt(document.getElementById('dailyTargetInput').value);
        const singleDrink = parseInt(document.getElementById('singleDrinkInput').value);
        const floatPosition = document.getElementById('floatPositionSelect').value;
        const circleSize = document.getElementById('circleSizeSelect').value;
        const enableAnimations = document.getElementById('enableAnimationsToggle').checked;

        // 验证输入
        if (dailyTarget < 500 || dailyTarget > 10000) {
            alert('每日目标应在500-10000ml之间');
            return;
        }
        if (singleDrink < 50 || singleDrink > 2000) {
            alert('单次饮水量应在50-2000ml之间');
            return;
        }

        // 更新数据
        watertimeData.dailyTarget = dailyTarget;
        watertimeData.singleDrink = singleDrink;
        watertimeData.floatPosition = floatPosition;
        watertimeData.circleSize = circleSize;
        watertimeData.enableAnimations = enableAnimations;

        // 保存到存储
        await chrome.storage.local.set({
            dailyTarget: dailyTarget,
            singleDrink: singleDrink,
            floatPosition: floatPosition,
            circleSize: circleSize,
            enableAnimations: enableAnimations
        });

        // 更新显示
        updateDisplay();

        // 显示成功反馈
        const saveBtn = document.getElementById('saveSettings');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '已保存';
        saveBtn.style.background = '#32CD32';

        setTimeout(() => {
            saveBtn.textContent = originalText;
            saveBtn.style.background = '';
            hideSettings();
        }, 1000);

        console.log('设置保存成功');
    } catch (error) {
        console.error('设置保存失败:', error);
        alert('设置保存失败，请重试');
    }
}

// 重置设置
async function resetSettings() {
    if (confirm('确定要重置所有设置吗？')) {
        try {
            // 重置为默认值
            watertimeData.dailyTarget = 2000;
            watertimeData.singleDrink = 200;
            watertimeData.floatPosition = 'bottom-right';
            watertimeData.circleSize = 'medium';
            watertimeData.enableAnimations = true;

            // 保存到存储
            await chrome.storage.local.set({
                dailyTarget: 2000,
                singleDrink: 200,
                floatPosition: 'bottom-right',
                circleSize: 'medium',
                enableAnimations: true
            });

            // 更新表单和显示
            initializeSettings();
            updateDisplay();

            console.log('设置重置成功');
        } catch (error) {
            console.error('设置重置失败:', error);
            alert('设置重置失败，请重试');
        }
    }
}

// 处理存储变化
function handleStorageChange(changes, namespace) {
    if (namespace === 'local') {
        let needUpdate = false;
        
        if (changes.dailyTarget) {
            watertimeData.dailyTarget = changes.dailyTarget.newValue;
            needUpdate = true;
        }
        
        if (changes.singleDrink) {
            watertimeData.singleDrink = changes.singleDrink.newValue;
            needUpdate = true;
        }
        
        // 检查今日数据变化
        const today = getTodayKey();
        if (changes[today]) {
            const newData = changes[today].newValue;
            if (newData) {
                watertimeData.todayTotal = newData.total || 0;
                needUpdate = true;
            }
        }
        
        if (needUpdate) {
            updateDisplay();
        }
    }
}

// 保存数据
async function saveData() {
    try {
        const today = getTodayKey();
        const todayData = {
            total: watertimeData.todayTotal
        };
        
        await chrome.storage.local.set({
            [today]: todayData
        });
        
        console.log('数据保存成功');
    } catch (error) {
        console.error('数据保存失败:', error);
        throw error;
    }
}

// 显示成功反馈
function showSuccessFeedback() {
    quickDrinkButton.style.background = 'rgba(50, 205, 50, 0.3)';
    quickDrinkButton.style.color = '#32CD32';
    
    setTimeout(() => {
        quickDrinkButton.style.background = '';
        quickDrinkButton.style.color = '';
    }, 1000);
}

// 显示错误反馈
function showErrorFeedback() {
    quickDrinkButton.style.background = 'rgba(255, 99, 71, 0.3)';
    quickDrinkButton.style.color = '#FF6347';
    
    setTimeout(() => {
        quickDrinkButton.style.background = '';
        quickDrinkButton.style.color = '';
    }, 1000);
}

// 获取今日键名
function getTodayKey() {
    const today = new Date();
    return `watertime_${today.getFullYear()}_${today.getMonth() + 1}_${today.getDate()}`;
}

// 获取指定日期的键值
function getDateKey(year, month, day) {
    return `watertime_${year}_${month + 1}_${day}`;
}

// 加载日历数据
async function loadCalendarData() {
    try {
        // 加载当前月份的数据
        const monthKey = `calendar_${calendarState.currentYear}_${calendarState.currentMonth}`;
        const result = await chrome.storage.local.get([monthKey]);

        if (result[monthKey]) {
            calendarState.monthlyData = result[monthKey];
        } else {
            calendarState.monthlyData = {};
        }

        console.log('日历数据加载完成:', calendarState.monthlyData);
    } catch (error) {
        console.error('日历数据加载失败:', error);
    }
}

// 更新日历显示
function updateCalendar() {
    const year = calendarState.currentYear;
    const month = calendarState.currentMonth;

    // 更新月份标题
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    currentMonthElement.textContent = `${year}年${monthNames[month]}`;

    // 生成日历天数
    generateCalendarDays(year, month);
}

// 生成日历天数
function generateCalendarDays(year, month) {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startWeekday = firstDay.getDay();

    // 清空现有内容
    calendarDaysElement.innerHTML = '';

    // 添加上个月的尾部日期
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();

    for (let i = startWeekday - 1; i >= 0; i--) {
        const day = prevMonthLastDay - i;
        const dayElement = createDayElement(prevYear, prevMonth, day, true);
        calendarDaysElement.appendChild(dayElement);
    }

    // 添加当前月的日期
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = createDayElement(year, month, day, false);
        calendarDaysElement.appendChild(dayElement);
    }

    // 添加下个月的开头日期
    const totalCells = calendarDaysElement.children.length;
    const remainingCells = 42 - totalCells; // 6行 × 7列
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextYear = month === 11 ? year + 1 : year;

    for (let day = 1; day <= remainingCells; day++) {
        const dayElement = createDayElement(nextYear, nextMonth, day, true);
        calendarDaysElement.appendChild(dayElement);
    }
}

// 创建日期元素
function createDayElement(year, month, day, isOtherMonth) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';
    dayElement.textContent = day;

    if (isOtherMonth) {
        dayElement.classList.add('other-month');
    }

    // 检查是否是今天
    const today = new Date();
    const isToday = year === today.getFullYear() &&
                   month === today.getMonth() &&
                   day === today.getDate();

    if (isToday) {
        dayElement.classList.add('today');
    }

    // 检查是否是选中的日期
    if (calendarState.selectedDate &&
        calendarState.selectedDate.year === year &&
        calendarState.selectedDate.month === month &&
        calendarState.selectedDate.day === day) {
        dayElement.classList.add('selected');
    }

    // 获取该日期的饮水数据
    const dateKey = getDateKey(year, month, day);
    const dayData = getDayData(dateKey);

    if (dayData) {
        const percentage = (dayData.total / watertimeData.dailyTarget) * 100;

        if (percentage >= 100) {
            dayElement.classList.add('achieved');
        } else if (percentage >= 50) {
            dayElement.classList.add('partial');
        } else if (percentage > 0) {
            dayElement.classList.add('missed');
        }
    }

    // 添加点击事件 - 所有日期都可以点击
    if (!isOtherMonth) {
        dayElement.addEventListener('click', () => {
            selectDate(year, month, day, dayData);
        });
        dayElement.style.cursor = 'pointer';
    }

    return dayElement;
}

// 获取指定日期的数据
function getDayData(dateKey) {
    // 从存储中获取数据（这里简化处理，实际应该从chrome.storage获取）
    return calendarState.monthlyData[dateKey] || null;
}

// 选择日期
function selectDate(year, month, day, dayData) {
    // 更新选中的日期
    calendarState.selectedDate = { year, month, day };

    // 更新日历显示（重新渲染以显示选中状态）
    updateCalendar();

    // 检查是否是今天
    const today = new Date();
    const isToday = year === today.getFullYear() &&
                   month === today.getMonth() &&
                   day === today.getDate();

    if (isToday) {
        // 如果选择的是今天，显示当前数据并启用操作按钮
        updateDisplayForToday();
        enableTodayOperations();
    } else {
        // 如果选择的是其他日期，显示历史数据并禁用操作按钮
        updateDisplayForSelectedDate(year, month, day, dayData);
        disableTodayOperations();
    }

    console.log(`选择日期: ${year}-${month + 1}-${day}`);
}

// 显示日期详情
function showDayDetails(year, month, day, dayData) {
    const percentage = Math.round((dayData.total / watertimeData.dailyTarget) * 100);
    const status = percentage >= 100 ? '已达标' :
                  percentage >= 50 ? '部分完成' : '未达标';

    alert(`${year}年${month + 1}月${day}日\n` +
          `饮水量: ${dayData.total}ml\n` +
          `目标: ${watertimeData.dailyTarget}ml\n` +
          `完成度: ${percentage}%\n` +
          `状态: ${status}`);
}

// 更新显示为今天的数据
function updateDisplayForToday() {
    // 重新加载今天的数据并更新显示
    updateDisplay();
}

// 更新显示为选中日期的数据
function updateDisplayForSelectedDate(year, month, day, dayData) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

    if (dayData) {
        // 有数据的日期
        const percentage = Math.round((dayData.total / watertimeData.dailyTarget) * 100);
        const remaining = Math.max(0, watertimeData.dailyTarget - dayData.total);

        // 更新进度显示
        percentageElement.textContent = `${percentage}%`;
        amountElement.textContent = `${dayData.total}ml / ${watertimeData.dailyTarget}ml`;
        remainingAmountElement.textContent = remaining > 0 ? `还需 ${remaining}ml` : '已完成目标';

        // 更新进度条颜色
        updateProgressColor(percentage);

        console.log(`显示历史数据: ${dateStr}, 饮水量: ${dayData.total}ml, 完成度: ${percentage}%`);
    } else {
        // 没有数据的日期
        percentageElement.textContent = '0%';
        amountElement.textContent = `0ml / ${watertimeData.dailyTarget}ml`;
        remainingAmountElement.textContent = `还需 ${watertimeData.dailyTarget}ml`;

        // 重置进度条颜色
        updateProgressColor(0);

        console.log(`显示历史数据: ${dateStr}, 无饮水记录`);
    }
}

// 更新进度条颜色
function updateProgressColor(percentage) {
    const progressElement = document.querySelector('.progress');
    if (progressElement) {
        if (percentage >= 100) {
            progressElement.style.background = 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)';
        } else if (percentage >= 80) {
            progressElement.style.background = 'linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%)';
        } else if (percentage >= 50) {
            progressElement.style.background = 'linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)';
        } else {
            progressElement.style.background = 'linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%)';
        }
    }
}

// 启用今日操作按钮
function enableTodayOperations() {
    quickDrinkButton.disabled = false;
    quickUndoButton.disabled = false;
    clearTodayButton.disabled = false;

    // 移除禁用样式
    quickDrinkButton.classList.remove('disabled');
    quickUndoButton.classList.remove('disabled');
    clearTodayButton.classList.remove('disabled');

    // 更新按钮文本提示
    quickDrinkButton.title = '快速记录饮水';
    quickUndoButton.title = '撤回上次记录';
    clearTodayButton.title = '删除当天记录';

    console.log('启用今日操作按钮');
}

// 禁用今日操作按钮
function disableTodayOperations() {
    quickDrinkButton.disabled = true;
    quickUndoButton.disabled = true;
    clearTodayButton.disabled = true;

    // 添加禁用样式
    quickDrinkButton.classList.add('disabled');
    quickUndoButton.classList.add('disabled');
    clearTodayButton.classList.add('disabled');

    // 更新按钮文本提示
    quickDrinkButton.title = '只能在当天进行记录操作';
    quickUndoButton.title = '只能撤回当天的记录';
    clearTodayButton.title = '只能删除当天的记录';

    console.log('禁用今日操作按钮');
}

// 检查选中的日期是否为今天
function isSelectedDateToday() {
    if (!calendarState.selectedDate) {
        return false;
    }

    const today = new Date();
    return calendarState.selectedDate.year === today.getFullYear() &&
           calendarState.selectedDate.month === today.getMonth() &&
           calendarState.selectedDate.day === today.getDate();
}

// 保存当日数据到日历
async function saveDayToCalendar() {
    try {
        const today = new Date();
        const dateKey = getDateKey(today.getFullYear(), today.getMonth(), today.getDate());

        // 更新月度数据
        calendarState.monthlyData[dateKey] = {
            total: watertimeData.todayTotal,
            date: today.toISOString().split('T')[0]
        };

        // 保存到存储
        const monthKey = `calendar_${today.getFullYear()}_${today.getMonth()}`;
        await chrome.storage.local.set({
            [monthKey]: calendarState.monthlyData
        });

        // 更新日历显示
        updateCalendar();

        console.log('日历数据保存成功');
    } catch (error) {
        console.error('日历数据保存失败:', error);
    }
}

// 处理快速撤回
async function handleQuickUndo() {
    try {
        // 检查是否选择的是今天
        if (!isSelectedDateToday()) {
            showErrorFeedback('只能撤回当天的记录');
            return;
        }

        if (watertimeData.todayTotal <= 0) {
            showErrorFeedback('今日还没有饮水记录可以撤回');
            return;
        }

        console.log(`快速撤回饮水: ${watertimeData.singleDrink}ml`);

        // 添加点击效果
        quickUndoButton.style.transform = 'scale(0.95)';
        setTimeout(() => {
            quickUndoButton.style.transform = '';
        }, 150);

        // 更新数据
        watertimeData.todayTotal = Math.max(0, watertimeData.todayTotal - watertimeData.singleDrink);

        // 保存数据
        await saveData();

        // 保存到日历
        await saveDayToCalendar();

        // 更新显示
        updateDisplay();

        // 成功反馈
        showSuccessFeedback('撤回成功');

    } catch (error) {
        console.error('快速撤回失败:', error);
        showErrorFeedback('撤回失败，请重试');
    }
}

// 处理删除当天记录
async function handleClearToday() {
    try {
        // 检查是否选择的是今天
        if (!isSelectedDateToday()) {
            showErrorFeedback('只能删除当天的记录');
            return;
        }

        if (watertimeData.todayTotal <= 0) {
            showErrorFeedback('今日没有饮水记录可以删除');
            return;
        }

        // 确认删除
        const confirmed = confirm(`确定要删除今日所有饮水记录吗？\n当前记录：${watertimeData.todayTotal}ml`);
        if (!confirmed) {
            return;
        }

        console.log('删除当天所有饮水记录');

        // 添加点击效果
        clearTodayButton.style.transform = 'scale(0.95)';
        setTimeout(() => {
            clearTodayButton.style.transform = '';
        }, 150);

        // 清空数据
        watertimeData.todayTotal = 0;

        // 保存数据
        await saveData();

        // 保存到日历
        await saveDayToCalendar();

        // 更新显示
        updateDisplay();

        // 成功反馈
        showSuccessFeedback('当天记录已清空');

    } catch (error) {
        console.error('删除当天记录失败:', error);
        showErrorFeedback('删除失败，请重试');
    }
}

// 显示错误反馈
function showErrorFeedback(message = '操作失败') {
    // 创建错误提示
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        animation: slideInDown 0.3s ease;
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.style.animation = 'slideOutUp 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(errorDiv);
            }, 300);
        }
    }, 3000);
}


