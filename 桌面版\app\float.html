<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime Float</title>
    <link rel="stylesheet" href="css/float.css">
    <style>
        /* 确保完全透明 */
        html, body {
            background: transparent !important;
        }
        /* 只对拖拽手柄设置拖拽区域 */
        .drag-handle {
            -webkit-app-region: drag;
        }
        /* 其他元素允许正常交互 */
        .float-widget {
            -webkit-app-region: no-drag;
        }
    </style>
</head>
<body>
    <div class="float-container" id="floatContainer">
        <div class="float-widget" id="floatWidget">
            <!-- 拖拽手柄 -->
            <div class="drag-handle" id="dragHandle"></div>

            <!-- 进度圆圈 -->
            <div class="progress-container">
                <svg class="progress-circle" viewBox="0 0 156 156">
                    <!-- 背景圆圈 -->
                    <circle class="progress-bg" cx="78" cy="78" r="67.6"></circle>
                    <!-- 进度圆弧 -->
                    <circle class="progress-arc" cx="78" cy="78" r="67.6" id="progressArc"></circle>
                </svg>

                <!-- 中心内容 -->
                <div class="progress-content">
                    <div class="water-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z"/>
                        </svg>
                    </div>
                    <div class="percentage" id="floatPercentage">0%</div>
                    <div class="amount" id="floatAmount">0ml</div>
                </div>
            </div>
        </div>

        <!-- 右键菜单 -->
        <div class="context-menu" id="contextMenu">
            <div class="menu-item" data-action="quickDrink">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z"/>
                </svg>
                <span>快速记录</span>
            </div>
            <div class="menu-item" data-action="quickUndo">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.5,8C9.85,8 7.45,9 5.6,10.6L2,7V16H11L7.38,12.38C8.77,11.22 10.54,10.5 12.5,10.5C16.04,10.5 19.05,12.81 20.1,16L22.47,15.22C21.08,11.03 17.15,8 12.5,8Z"/>
                </svg>
                <span>快速撤销</span>
            </div>
            <div class="menu-separator"></div>
            <div class="menu-item" data-action="openMain">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"/>
                </svg>
                <span>打开主窗口</span>
            </div>
            <div class="menu-item" data-action="hide">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.74,7.13 11.35,7 12,7Z"/>
                </svg>
                <span>隐藏圆圈</span>
            </div>
        </div>
    </div>



    <!-- 数据管理器 -->
    <script src="js/dataManager.js"></script>
    <!-- 浮动圆圈脚本 -->
    <script src="js/float.js"></script>
</body>
</html>
