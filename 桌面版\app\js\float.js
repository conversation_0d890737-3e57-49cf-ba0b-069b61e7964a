// WaterTime 浮动圆圈脚本

class FloatWidget {
    constructor() {
        this.dataManager = null;
        this.data = {
            todayTotal: 0,
            dailyTarget: 2000
        };

        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.lastClickTime = 0;

        this.init();
    }

    async init() {
        try {
            console.log('[FloatWidget] 开始初始化浮动圆圈...');

            // 先绑定最基本的事件，测试交互
            this.bindSimpleEvents();

            // 初始化UI
            this.initializeUI();

            console.log('[FloatWidget] 基本初始化完成');

            // 异步加载数据管理器
            this.initDataManagerAsync();

            console.log('[FloatWidget] 浮动圆圈初始化完成');
        } catch (error) {
            console.error('[FloatWidget] 浮动圆圈初始化失败:', error);
            this.setStatusIndicator('error');
        }
    }

    // 异步初始化数据管理器
    async initDataManagerAsync() {
        try {
            console.log('[FloatWidget] 开始异步加载数据管理器...');

            // 使用全局数据管理器实例
            this.dataManager = window.dataManager;
            console.log('[FloatWidget] 使用全局数据管理器实例');

            // 确保数据管理器已初始化
            if (!this.dataManager.isInitialized) {
                await this.dataManager.initialize();
            }

            // 加载数据
            await this.loadData();

            // 设置数据变更监听
            this.setupDataListeners();

            // 绑定完整事件（包括拖拽和数据相关事件）
            this.bindEvents();

            // 更新进度显示
            this.updateProgress();
            this.addProgressGradient();

            // 设置性能优化
            this.setupPerformanceOptimizations();

            // 设置IPC监听器
            this.setupIpcListeners();

            // 设置定时数据同步
            this.setupDataSync();

            console.log('[FloatWidget] 数据管理器初始化完成');
        } catch (error) {
            console.error('[FloatWidget] 数据管理器初始化失败:', error);
            this.setStatusIndicator('error');
        }
    }

    // 初始化UI
    initializeUI() {
        // 设置初始状态
        this.setStatusIndicator('normal');

        // 隐藏右键菜单
        this.hideContextMenu();

        // 设置可见性监听
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        console.log('[FloatWidget] UI初始化完成');
    }

    // 设置性能优化
    setupPerformanceOptimizations() {
        // 防抖窗口大小变化
        this.handleWindowResize = this.debounce(this.handleWindowResize.bind(this), 250);

        // 初始化可见性状态
        this.isVisible = !document.hidden;

        console.log('[FloatWidget] 性能优化设置完成');
    }

    // 绑定基本事件（保持拖拽功能，添加数据同步）
    bindSimpleEvents() {
        console.log('[FloatWidget] 绑定基本事件...');

        const widget = document.getElementById('floatWidget');
        if (!widget) {
            console.error('[FloatWidget] 找不到floatWidget元素');
            return;
        }

        // 点击添加饮水记录
        widget.addEventListener('click', async (e) => {
            console.log('[FloatWidget] 🎯 点击事件触发！');

            // 防止拖拽时触发点击
            if (this.isDragging) {
                console.log('[FloatWidget] 正在拖拽，忽略点击');
                return;
            }

            // 添加饮水记录
            await this.handleQuickAdd();
        });

        // 悬浮效果
        widget.addEventListener('mouseenter', (e) => {
            console.log('[FloatWidget] 🎯 鼠标进入！');
            widget.style.transform = 'scale(1.05)';
            widget.style.transition = 'all 0.3s ease';
        });

        widget.addEventListener('mouseleave', (e) => {
            console.log('[FloatWidget] 🎯 鼠标离开！');
            widget.style.transform = 'scale(1)';
        });

        console.log('[FloatWidget] 基本事件绑定完成');
    }

    // 快速添加饮水记录
    async handleQuickAdd() {
        try {
            console.log('[FloatWidget] 🚰 快速添加饮水记录...');

            if (!this.dataManager) {
                console.error('[FloatWidget] 数据管理器未初始化');
                return;
            }

            // 默认添加200ml
            const amount = 200;
            const success = await this.dataManager.addDrinkRecord(amount);

            if (success) {
                console.log(`[FloatWidget] ✅ 成功添加 ${amount}ml 饮水记录`);

                // 立即重新加载数据并更新显示
                await this.loadData();

                // 显示添加成功的视觉反馈
                this.showAddFeedback(amount);
            } else {
                console.error('[FloatWidget] ❌ 添加饮水记录失败');
            }
        } catch (error) {
            console.error('[FloatWidget] 快速添加饮水记录出错:', error);
        }
    }

    // 显示添加成功的视觉反馈
    showAddFeedback(amount) {
        const widget = document.getElementById('floatWidget');
        if (!widget) return;

        // 创建临时提示
        const feedback = document.createElement('div');
        feedback.textContent = `+${amount}ml`;
        feedback.style.cssText = `
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 191, 255, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            z-index: 1000;
            animation: fadeInOut 2s ease-in-out forwards;
        `;

        // 添加动画样式
        if (!document.getElementById('feedbackStyle')) {
            const style = document.createElement('style');
            style.id = 'feedbackStyle';
            style.textContent = `
                @keyframes fadeInOut {
                    0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
                    20% { opacity: 1; transform: translateX(-50%) translateY(0); }
                    80% { opacity: 1; transform: translateX(-50%) translateY(0); }
                    100% { opacity: 0; transform: translateX(-50%) translateY(-10px); }
                }
            `;
            document.head.appendChild(style);
        }

        widget.appendChild(feedback);

        // 2秒后移除
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }

    // 等待数据管理器加载
    async waitForDataManager() {
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时

        console.log('[FloatWidget] Waiting for data manager...');

        while (!window.dataManager && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.dataManager) {
            console.error('[FloatWidget] Data manager load timeout');
            throw new Error('Data manager load timeout');
        }

        console.log('[FloatWidget] Data manager loaded successfully');
        this.dataManager = window.dataManager;
    }

    // 设置数据变更监听 - 简化版本，避免死循环
    setupDataListeners() {
        console.log('[FloatWidget] 设置数据监听器...');

        try {
            // 监听设置变更
            this.dataManager.addListener('settings', (settings) => {
                console.log('[FloatWidget] 设置已更新:', settings);
                this.data.dailyTarget = settings.dailyTarget;
                this.updateProgress();
            });

            // 监听今日数据变更
            this.dataManager.addListener('todayData', (todayData) => {
                console.log('[FloatWidget] 今日数据已更新:', todayData);
                this.data.todayTotal = todayData.total;
                this.updateProgress();
            });

            // 监听每日重置
            this.dataManager.addListener('dailyReset', () => {
                console.log('[FloatWidget] 检测到每日重置');
                this.loadData(); // 重新加载数据
            });

            console.log('[FloatWidget] 数据监听器设置完成');
        } catch (error) {
            console.error('[FloatWidget] 设置数据监听器失败:', error);
        }
    }

    // 加载数据 - 简化版本，直接使用DataManager
    async loadData() {
        try {
            console.log('[FloatWidget] 开始加载数据...');

            // 加载设置
            const settings = await this.dataManager.getSettings();
            this.data.dailyTarget = settings.dailyTarget;

            // 加载今日数据
            const todayData = await this.dataManager.getTodayData();
            this.data.todayTotal = todayData.total || 0;

            // 更新显示
            this.updateProgress();

            console.log('[FloatWidget] 数据加载完成:', this.data);
        } catch (error) {
            console.error('[FloatWidget] 数据加载失败:', error);
            // 设置默认值
            this.data.dailyTarget = 2000;
            this.data.todayTotal = 0;
            this.updateProgress();
        }
    }

    // 设置IPC监听器
    setupIpcListeners() {
        try {
            console.log('[FloatWidget] 设置IPC监听器...');

            // 监听强制数据刷新消息
            if (window.electronAPI && window.electronAPI.onForceDataRefresh) {
                window.electronAPI.onForceDataRefresh(() => {
                    console.log('[FloatWidget] 收到强制数据刷新消息');
                    this.loadData();
                });
            }

            console.log('[FloatWidget] IPC监听器设置完成');
        } catch (error) {
            console.error('[FloatWidget] 设置IPC监听器失败:', error);
        }
    }

    // 设置定时数据同步
    setupDataSync() {
        try {
            console.log('[FloatWidget] 设置定时数据同步...');

            // 每5秒检查一次数据是否需要更新
            this.dataSyncInterval = setInterval(async () => {
                try {
                    // 获取最新数据
                    const settings = await this.dataManager.getSettings();
                    const todayData = await this.dataManager.getTodayData();

                    // 检查数据是否有变化
                    if (settings.dailyTarget !== this.data.dailyTarget ||
                        todayData.total !== this.data.todayTotal) {

                        console.log('[FloatWidget] 检测到数据变化，更新显示');
                        this.data.dailyTarget = settings.dailyTarget;
                        this.data.todayTotal = todayData.total || 0;
                        this.updateProgress();
                    }
                } catch (error) {
                    console.error('[FloatWidget] 定时数据同步失败:', error);
                }
            }, 5000); // 5秒间隔

            console.log('[FloatWidget] 定时数据同步已设置');
        } catch (error) {
            console.error('[FloatWidget] 设置定时数据同步失败:', error);
        }
    }

    // 定时刷新数据（替代复杂的广播机制）
    async refreshData() {
        try {
            console.log('[FloatWidget] 刷新数据...');

            // 重新加载数据
            await this.loadData();

            console.log('[FloatWidget] 数据刷新完成');
        } catch (error) {
            console.error('[FloatWidget] 数据刷新失败:', error);
        }
    }

    // 绑定基本事件（不依赖数据管理器）- Google Material Design风格
    bindBasicEvents() {
        console.log('[FloatWidget] 绑定基本事件...');

        const widget = document.getElementById('floatWidget');
        if (!widget) {
            console.error('[FloatWidget] 找不到floatWidget元素');
            return;
        }

        // Material Design 悬浮效果（保存引用以便后续移除）
        this.basicMouseEnterHandler = () => {
            console.log('[FloatWidget] 鼠标进入（基本）');
            widget.style.transform = 'scale(1.08) translateY(-2px)';
            widget.style.transition = 'all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
            widget.style.boxShadow = '0 8px 25px rgba(0, 191, 255, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15)';
            widget.style.filter = 'brightness(1.1)';
        };

        this.basicMouseLeaveHandler = () => {
            console.log('[FloatWidget] 鼠标离开（基本）');
            widget.style.transform = 'scale(1) translateY(0)';
            widget.style.boxShadow = '0 4px 12px rgba(0, 191, 255, 0.2), 0 2px 6px rgba(0, 0, 0, 0.1)';
            widget.style.filter = 'brightness(1)';
        };

        widget.addEventListener('mouseenter', this.basicMouseEnterHandler);
        widget.addEventListener('mouseleave', this.basicMouseLeaveHandler);

        // Material Design 点击波纹效果（保存引用以便后续移除）
        this.basicMouseDownHandler = (e) => {
            console.log('[FloatWidget] 鼠标按下（基本）');
            widget.style.transform = 'scale(0.96) translateY(0)';
            widget.style.transition = 'all 0.15s cubic-bezier(0.4, 0.0, 0.6, 1)';
        };

        this.basicMouseUpHandler = () => {
            console.log('[FloatWidget] 鼠标释放（基本）');
            widget.style.transform = 'scale(1.08) translateY(-2px)';
            widget.style.transition = 'all 0.2s cubic-bezier(0.0, 0.0, 0.2, 1)';
        };

        this.basicClickHandler = (e) => {
            console.log('[FloatWidget] 基本点击（仅动画）');
        };

        widget.addEventListener('mousedown', this.basicMouseDownHandler);
        widget.addEventListener('mouseup', this.basicMouseUpHandler);
        widget.addEventListener('click', this.basicClickHandler);

        console.log('[FloatWidget] 基本事件绑定完成');
    }

    // 绑定完整事件（依赖数据管理器）
    bindEvents() {
        console.log('[FloatWidget] 绑定完整事件...');

        const container = document.getElementById('floatContainer');
        const widget = document.getElementById('floatWidget');

        // 拖拽事件
        this.bindDragEvents();

        // 快速操作按钮
        this.bindQuickActions();

        // 右键菜单
        this.bindContextMenu();

        // 移除之前的基本点击事件，避免冲突
        widget.removeEventListener('click', this.basicClickHandler);
        widget.removeEventListener('mousedown', this.basicMouseDownHandler);
        widget.removeEventListener('mouseup', this.basicMouseUpHandler);

        // 左键点击事件（业务逻辑）
        widget.addEventListener('click', this.handleDirectClick.bind(this));

        // 双击事件
        widget.addEventListener('dblclick', this.handleDoubleClick.bind(this));

        // 鼠标悬停事件（覆盖基本事件）
        widget.removeEventListener('mouseenter', this.basicMouseEnterHandler);
        widget.removeEventListener('mouseleave', this.basicMouseLeaveHandler);
        widget.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        widget.addEventListener('mouseleave', this.handleMouseLeave.bind(this));

        // 监听数据更新（保留作为备用）
        if (window.electronAPI && window.electronAPI.onDataUpdate) {
            console.log('[FloatWidget] 设置IPC数据更新监听器');
            this.ipcDataUpdateRemover = window.electronAPI.onDataUpdate(this.handleDataUpdate.bind(this));
        } else {
            console.warn('[FloatWidget] electronAPI.onDataUpdate 不可用');
        }

        // 添加定时数据刷新，减少对广播的依赖
        this.setupDataRefresh();

        // 监听窗口大小变化
        window.addEventListener('resize', this.handleWindowResize.bind(this));

        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    // 绑定拖拽事件
    bindDragEvents() {
        const dragHandle = document.getElementById('dragHandle');
        const widget = document.getElementById('floatWidget');

        // 鼠标拖拽
        dragHandle.addEventListener('mousedown', this.handleDragStart.bind(this));
        document.addEventListener('mousemove', this.handleDragMove.bind(this));
        document.addEventListener('mouseup', this.handleDragEnd.bind(this));

        // 触摸拖拽（如果需要）
        dragHandle.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
    }

    // 绑定快速操作
    bindQuickActions() {
        document.getElementById('quickAdd').addEventListener('click', this.handleQuickAdd.bind(this));
        document.getElementById('quickUndo').addEventListener('click', this.handleQuickUndo.bind(this));
        document.getElementById('openMain').addEventListener('click', this.handleOpenMain.bind(this));
    }

    // 绑定右键菜单
    bindContextMenu() {
        const widget = document.getElementById('floatWidget');
        const contextMenu = document.getElementById('contextMenu');

        widget.addEventListener('contextmenu', this.handleContextMenu.bind(this));

        // 菜单项点击
        contextMenu.addEventListener('click', this.handleMenuClick.bind(this));

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
            if (!contextMenu.contains(e.target) && !widget.contains(e.target)) {
                this.hideContextMenu();
            }
        });
    }

    // ==================== 拖拽处理 ====================

    // 开始拖拽
    handleDragStart(e) {
        e.preventDefault();
        e.stopPropagation();

        // 获取鼠标在屏幕上的位置（兼容性处理）
        let screenX, screenY;

        if (e.touches && e.touches[0]) {
            // 触摸事件
            screenX = e.touches[0].screenX || (e.touches[0].clientX + window.screenX);
            screenY = e.touches[0].screenY || (e.touches[0].clientY + window.screenY);
        } else {
            // 鼠标事件
            screenX = e.screenX || (e.clientX + window.screenX);
            screenY = e.screenY || (e.clientY + window.screenY);
        }

        // 计算鼠标相对于窗口左上角的偏移量
        this.dragOffset = {
            x: screenX - window.screenX,
            y: screenY - window.screenY
        };

        this.isDragging = true;
        this.dragStartTime = Date.now();

        // 添加拖拽样式
        document.getElementById('floatContainer').classList.add('dragging');

        // 隐藏右键菜单
        this.hideContextMenu();

        console.log('[FloatWidget] 开始拖拽，窗口位置:', window.screenX, window.screenY);
        console.log('[FloatWidget] 鼠标屏幕位置:', screenX, screenY);
        console.log('[FloatWidget] 计算偏移量:', this.dragOffset);
    }

    // 拖拽移动（正确的实现）
    handleDragMove(e) {
        if (!this.isDragging) return;

        e.preventDefault();

        // 获取鼠标在屏幕上的当前位置（兼容性处理）
        let screenX, screenY;

        if (e.touches && e.touches[0]) {
            // 触摸事件
            screenX = e.touches[0].screenX || (e.touches[0].clientX + window.screenX);
            screenY = e.touches[0].screenY || (e.touches[0].clientY + window.screenY);
        } else {
            // 鼠标事件
            screenX = e.screenX || (e.clientX + window.screenX);
            screenY = e.screenY || (e.clientY + window.screenY);
        }

        // 计算窗口应该在的位置 = 鼠标屏幕位置 - 偏移量
        const newX = screenX - this.dragOffset.x;
        const newY = screenY - this.dragOffset.y;

        // 直接更新窗口位置
        if (window.electronAPI.updateFloatPosition) {
            window.electronAPI.updateFloatPosition(newX, newY);
        }
    }

    // 结束拖拽
    handleDragEnd(e) {
        if (!this.isDragging) return;

        this.isDragging = false;
        document.getElementById('floatContainer').classList.remove('dragging');

        // 如果拖拽时间很短且移动距离很小，视为点击事件
        const dragDuration = Date.now() - this.dragStartTime;
        const clientX = e.clientX || (e.changedTouches && e.changedTouches[0].clientX);
        const clientY = e.clientY || (e.changedTouches && e.changedTouches[0].clientY);

        if (clientX && clientY) {
            const deltaX = Math.abs(clientX - this.dragOffset.x);
            const deltaY = Math.abs(clientY - this.dragOffset.y);
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (dragDuration < 300 && distance < 10) {
                this.handleClick();
            }
        }

        console.log('[FloatWidget] 拖拽结束');
    }

    // 触摸事件处理
    handleTouchStart(e) {
        this.handleDragStart(e);
    }

    handleTouchMove(e) {
        this.handleDragMove(e);
    }

    handleTouchEnd(e) {
        this.handleDragEnd(e);
    }

    // 获取屏幕边界
    getScreenBounds() {
        return {
            width: window.screen.availWidth,
            height: window.screen.availHeight,
            left: window.screen.availLeft || 0,
            top: window.screen.availTop || 0
        };
    }

    // 限制窗口在屏幕内
    constrainToScreen(x, y, bounds) {
        const windowWidth = 80;   // 更新为新的窗口尺寸
        const windowHeight = 80;  // 更新为新的窗口尺寸
        const margin = 10;

        const minX = bounds.left + margin;
        const maxX = bounds.left + bounds.width - windowWidth - margin;
        const minY = bounds.top + margin;
        const maxY = bounds.top + bounds.height - windowHeight - margin;

        return {
            x: Math.max(minX, Math.min(maxX, x)),
            y: Math.max(minY, Math.min(maxY, y))
        };
    }

    // ==================== 交互事件处理 ====================

    // 直接点击事件（不依赖拖拽检测）
    async handleDirectClick(e) {
        console.log('[FloatWidget] 点击事件触发');

        // 防止在拖拽过程中触发点击
        if (this.isDragging) {
            console.log('[FloatWidget] 正在拖拽，忽略点击');
            return;
        }

        // 防止右键点击触发
        if (e.button !== 0) {
            console.log('[FloatWidget] 非左键点击，忽略');
            return;
        }

        const now = Date.now();

        // 防止双击时触发单击
        if (now - this.lastClickTime < 300) {
            console.log('[FloatWidget] 点击间隔过短，忽略');
            return;
        }

        this.lastClickTime = now;

        // 立即添加点击动画
        this.addClickAnimation();

        // 快速记录饮水
        await this.handleQuickAdd();

        console.log('[FloatWidget] 左键点击 - 快速记录饮水完成');
    }

    // 单击事件（保留原有逻辑作为备用）
    async handleClick() {
        const now = Date.now();

        // 防止双击时触发单击
        if (now - this.lastClickTime < 300) {
            return;
        }

        this.lastClickTime = now;

        // 添加点击动画
        this.addClickAnimation();

        // 快速记录饮水
        await this.handleQuickAdd();
    }

    // 快速添加记录
    async handleQuickAdd() {
        console.log('[FloatWidget] 开始快速添加记录');

        try {
            // 防抖处理
            const now = Date.now();
            if (this.isProcessing || (now - this.lastProcessTime) < 500) {
                console.log('[FloatWidget] 操作过于频繁，忽略此次点击');
                return;
            }

            this.isProcessing = true;
            this.lastProcessTime = now;

            console.log('[FloatWidget] 获取设置和当前数据...');
            const settings = await this.dataManager.getSettings();
            const oldData = await this.dataManager.getTodayData();

            console.log('[FloatWidget] 当前数据:', oldData);
            console.log('[FloatWidget] 设置:', settings);

            // 添加记录
            console.log('[FloatWidget] 添加饮水记录:', settings.singleDrink, 'ml');
            const record = await this.dataManager.addDrinkRecord(settings.singleDrink);
            console.log('[FloatWidget] 记录添加成功:', record);

            // 获取更新后的数据
            const newData = await this.dataManager.getTodayData();
            console.log('[FloatWidget] 更新后数据:', newData);

            // 更新本地数据
            this.data.todayTotal = newData.total;
            this.data.dailyTarget = settings.dailyTarget;

            // 显示提示
            this.showTooltip(`已记录 ${settings.singleDrink}ml`);

            // 添加成功动画
            this.addSuccessAnimation();

            // 更新进度显示
            this.updateProgress();

            // 检查是否达成目标
            if (newData.total >= settings.dailyTarget && oldData.total < settings.dailyTarget) {
                this.showCelebration();
            }

            console.log('[FloatWidget] 快速记录饮水成功:', settings.singleDrink, '总量:', newData.total);
        } catch (error) {
            console.error('[FloatWidget] 快速记录失败:', error);
            this.showTooltip('记录失败');
            this.setStatusIndicator('error');
        } finally {
            this.isProcessing = false;
        }
    }

    // 快速撤销
    async handleQuickUndo() {
        try {
            const todayData = await this.dataManager.getTodayData();
            if (todayData.records.length === 0) {
                this.showTooltip('没有可撤销的记录');
                return;
            }

            const lastRecord = todayData.records[todayData.records.length - 1];
            await this.dataManager.removeDrinkRecord(lastRecord.id);

            this.showTooltip(`已撤销 ${lastRecord.amount}ml`);
            console.log('[FloatWidget] 撤销记录:', lastRecord.amount);
        } catch (error) {
            console.error('[FloatWidget] 撤销失败:', error);
            this.showTooltip('撤销失败');
            this.setStatusIndicator('error');
        }
    }

    // 打开主窗口
    async handleOpenMain() {
        try {
            await window.electronAPI.showMainWindow();
            console.log('[FloatWidget] 打开主窗口');
        } catch (error) {
            console.error('[FloatWidget] 打开主窗口失败:', error);
        }
    }

    // 鼠标进入
    handleMouseEnter() {
        // 可以添加悬停效果
        this.setStatusIndicator('active');
    }

    // 鼠标离开
    handleMouseLeave() {
        // 恢复正常状态
        this.setStatusIndicator('normal');
    }

    // 双击事件
    handleDoubleClick() {
        console.log('[FloatWidget] 双击 - 打开主窗口');
        this.handleOpenMain();
    }

    // 窗口大小变化
    handleWindowResize() {
        // 确保窗口仍在屏幕内
        if (!this.isDragging) {
            const bounds = this.getScreenBounds();
            const currentX = window.screenX;
            const currentY = window.screenY;
            const constrainedPosition = this.constrainToScreen(currentX, currentY, bounds);

            if (constrainedPosition.x !== currentX || constrainedPosition.y !== currentY) {
                window.electronAPI.updateFloatPosition(constrainedPosition.x, constrainedPosition.y);
            }
        }
    }

    // 键盘事件
    handleKeyDown(e) {
        // 快捷键支持
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    this.handleQuickAdd();
                    break;
                case 'Backspace':
                    e.preventDefault();
                    this.handleQuickUndo();
                    break;
                case 'h':
                    e.preventDefault();
                    this.hideFloatWindow();
                    break;
            }
        }
    }

    // 双击事件
    handleDoubleClick() {
        console.log('浮动圆圈被双击 - 打开主窗口');
        // 这里可以通过 IPC 通信打开主窗口
    }

    // ==================== 右键菜单 ====================

    // 显示右键菜单
    handleContextMenu(e) {
        e.preventDefault();
        e.stopPropagation();

        const contextMenu = document.getElementById('contextMenu');

        // 先显示菜单以获取其尺寸
        contextMenu.style.visibility = 'hidden';
        contextMenu.style.opacity = '1';
        contextMenu.classList.add('show');

        // 获取菜单尺寸
        const menuRect = contextMenu.getBoundingClientRect();
        const menuWidth = menuRect.width;
        const menuHeight = menuRect.height;

        // 获取屏幕尺寸
        const screenBounds = this.getScreenBounds();
        const screenWidth = screenBounds.width;
        const screenHeight = screenBounds.height;

        // 计算鼠标在屏幕上的位置
        const mouseScreenX = window.screenX + e.clientX;
        const mouseScreenY = window.screenY + e.clientY;

        // 计算菜单位置（屏幕坐标）
        let menuScreenX = mouseScreenX + 10; // 鼠标右侧10px
        let menuScreenY = mouseScreenY;

        // 如果菜单超出屏幕右边界，显示在鼠标左侧
        if (menuScreenX + menuWidth > screenBounds.left + screenWidth) {
            menuScreenX = mouseScreenX - menuWidth - 10;
        }

        // 如果菜单超出屏幕下边界，向上调整
        if (menuScreenY + menuHeight > screenBounds.top + screenHeight) {
            menuScreenY = mouseScreenY - menuHeight;
        }

        // 确保菜单不超出屏幕边界
        menuScreenX = Math.max(screenBounds.left, Math.min(menuScreenX, screenBounds.left + screenWidth - menuWidth));
        menuScreenY = Math.max(screenBounds.top, Math.min(menuScreenY, screenBounds.top + screenHeight - menuHeight));

        // 设置最终位置（屏幕坐标）
        contextMenu.style.left = `${menuScreenX}px`;
        contextMenu.style.top = `${menuScreenY}px`;
        contextMenu.style.visibility = 'visible';

        console.log('[FloatWidget] 显示右键菜单', {
            mouseScreenX, mouseScreenY,
            menuScreenX, menuScreenY,
            menuWidth, menuHeight
        });
    }

    // 隐藏右键菜单
    hideContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        contextMenu.classList.remove('show');
    }

    // 菜单项点击
    async handleMenuClick(e) {
        const menuItem = e.target.closest('.menu-item');
        if (!menuItem) return;

        const action = menuItem.dataset.action;
        this.hideContextMenu();

        switch (action) {
            case 'quickDrink':
                await this.handleQuickAdd();
                break;
            case 'quickUndo':
                await this.handleQuickUndo();
                break;
            case 'openMain':
                await this.handleOpenMain();
                break;
            case 'hide':
                this.hideFloatWindow();
                break;
        }
    }

    // 隐藏浮动窗口
    async hideFloatWindow() {
        try {
            await window.electronAPI.toggleFloatWindow();
            console.log('[FloatWidget] 隐藏浮动窗口');
        } catch (error) {
            console.error('[FloatWidget] 隐藏窗口失败:', error);
        }
    }

    // 设置定时数据刷新
    setupDataRefresh() {
        // 每30秒刷新一次数据，确保与主窗口同步
        this.dataRefreshInterval = setInterval(async () => {
            if (this.isVisible && !this.isDragging) {
                try {
                    await this.loadData();
                    console.log('[FloatWidget] 定时数据刷新完成');
                } catch (error) {
                    console.error('[FloatWidget] 定时数据刷新失败:', error);
                }
            }
        }, 30000); // 30秒间隔

        console.log('[FloatWidget] 定时数据刷新已设置');
    }

    // 数据更新处理 - 简化版：直接重新加载数据
    async handleDataUpdate(data) {
        console.log('[FloatWidget] 收到数据更新通知，直接重新加载数据');

        try {
            // 直接重新加载最新数据，而不是依赖广播的数据
            await this.loadData();
            console.log('[FloatWidget] 数据重新加载完成');
        } catch (error) {
            console.error('[FloatWidget] 重新加载数据失败:', error);
        }
    }

    // 更新进度显示
    updateProgress() {
        if (!this.isVisible) {
            // 窗口不可见时跳过更新以节省性能
            return;
        }

        const percentage = Math.min(100, Math.round((this.data.todayTotal / this.data.dailyTarget) * 100));

        // 更新文字显示
        document.getElementById('floatPercentage').textContent = `${percentage}%`;
        document.getElementById('floatAmount').textContent = `${this.data.todayTotal}ml`;

        // 使用动画更新进度圆圈
        this.updateProgressWithAnimation(percentage);

        // 更新状态指示器
        this.updateStatusByProgress(percentage);

        console.log(`[FloatWidget] 进度更新: ${percentage}% (${this.data.todayTotal}ml/${this.data.dailyTarget}ml)`);
    }

    // 根据进度更新状态
    updateStatusByProgress(percentage) {
        if (percentage >= 100) {
            this.setStatusIndicator('normal'); // 完成目标
        } else if (percentage >= 75) {
            this.setStatusIndicator('normal'); // 进展良好
        } else if (percentage >= 50) {
            this.setStatusIndicator('warning'); // 需要努力
        } else {
            this.setStatusIndicator('warning'); // 进度不足
        }
    }

    // 更新进度圆弧（传统方式，用于兼容）
    updateProgressArc(percentage) {
        const progressArc = document.getElementById('progressArc');
        const circumference = 2 * Math.PI * 67.6; // 与HTML中的半径匹配
        const offset = circumference - (percentage / 100) * circumference;

        progressArc.style.strokeDashoffset = offset;

        // 根据进度改变颜色（使用CSS变量更好）
        if (percentage >= 100) {
            progressArc.style.stroke = '#22c55e'; // 绿色 - 完成
        } else if (percentage >= 75) {
            progressArc.style.stroke = 'url(#progressGradient)'; // 渐变 - 良好
        } else if (percentage >= 50) {
            progressArc.style.stroke = '#fbbf24'; // 黄色 - 一般
        } else {
            progressArc.style.stroke = '#ef4444'; // 红色 - 不足
        }
    }

    // ==================== 动画和视觉效果 ====================

    // 添加进度渐变（已在HTML中定义）
    addProgressGradient() {
        // 渐变已在HTML中定义，这里可以做一些动态调整
        console.log('[FloatWidget] 进度渐变已初始化');
    }

    // 添加点击动画
    addClickAnimation() {
        const widget = document.querySelector('.float-widget');
        if (widget) {
            widget.classList.add('clicked');
            setTimeout(() => {
                widget.classList.remove('clicked');
            }, 800);
        }
    }

    // 添加成功动画
    addSuccessAnimation() {
        const widget = document.querySelector('.float-widget');
        if (widget) {
            widget.classList.add('success');
            setTimeout(() => {
                widget.classList.remove('success');
            }, 1000);
        }
    }

    // 添加水波动画
    addWaterAnimation() {
        const waterAnimation = document.getElementById('waterAnimation');
        if (waterAnimation) {
            waterAnimation.classList.add('active');
            setTimeout(() => {
                waterAnimation.classList.remove('active');
            }, 3000);
        }
    }

    // 显示庆祝动画
    showCelebration() {
        console.log('🎉 恭喜达成今日饮水目标！');

        const widget = document.querySelector('.float-widget');
        if (widget) {
            widget.classList.add('celebration');
            setTimeout(() => {
                widget.classList.remove('celebration');
            }, 2000);
        }

        // 显示庆祝提示
        this.showTooltip('🎉 恭喜达成今日目标！');
    }

    // 设置状态指示器
    setStatusIndicator(status) {
        const statusDot = document.querySelector('.status-dot');

        // 清除所有状态类
        statusDot.classList.remove('warning', 'error', 'active');

        // 添加新状态类
        if (status !== 'normal') {
            statusDot.classList.add(status);
        }
    }

    // 显示提示
    showTooltip(message, duration = 2000) {
        const tooltip = document.getElementById('tooltip');
        tooltip.textContent = message;
        tooltip.classList.add('show');

        // 清除之前的定时器
        if (this.tooltipTimer) {
            clearTimeout(this.tooltipTimer);
        }

        // 设置自动隐藏
        this.tooltipTimer = setTimeout(() => {
            tooltip.classList.remove('show');
        }, duration);
    }

    // 更新进度动画
    updateProgressWithAnimation(percentage) {
        const progressArc = document.getElementById('progressArc');
        if (!progressArc) return;

        const circumference = 2 * Math.PI * 67.6; // 与HTML中的半径匹配
        const offset = circumference - (percentage / 100) * circumference;

        // 取消之前的动画
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        // 使用 requestAnimationFrame 优化动画性能
        const animate = (currentOffset, targetOffset, startTime) => {
            const now = performance.now();
            const elapsed = now - startTime;
            const duration = 600; // 减少动画持续时间

            if (elapsed < duration) {
                const progress = elapsed / duration;
                const easeProgress = this.easeOutCubic(progress);
                const newOffset = currentOffset + (targetOffset - currentOffset) * easeProgress;

                progressArc.style.strokeDashoffset = newOffset;
                this.animationFrame = requestAnimationFrame(() => animate(currentOffset, targetOffset, startTime));
            } else {
                progressArc.style.strokeDashoffset = targetOffset;
                this.animationFrame = null;
            }
        };

        const currentOffset = parseFloat(progressArc.style.strokeDashoffset) || circumference;
        animate(currentOffset, offset, performance.now());
    }

    // 缓动函数
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    // ==================== 性能优化 ====================

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // 内存清理
    cleanup() {
        console.log('[FloatWidget] 开始清理资源...');

        // 清除定时器
        if (this.tooltipTimer) {
            clearTimeout(this.tooltipTimer);
            this.tooltipTimer = null;
        }

        // 取消动画
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }

        // 移除IPC监听器
        if (this.ipcDataUpdateRemover) {
            this.ipcDataUpdateRemover();
            this.ipcDataUpdateRemover = null;
        }

        // 清理定时刷新器
        if (this.dataRefreshInterval) {
            clearInterval(this.dataRefreshInterval);
            this.dataRefreshInterval = null;
        }

        // 移除数据管理器监听器
        if (this.dataManager) {
            this.dataManager.removeListener('settings', this.handleSettingsUpdate);
            this.dataManager.removeListener('todayData', this.handleDataUpdate);
        }

        console.log('[FloatWidget] 资源清理完成');
    }

    // 窗口可见性检测
    handleVisibilityChange() {
        if (document.hidden) {
            // 窗口隐藏时减少更新频率
            this.isVisible = false;
        } else {
            // 窗口显示时恢复正常更新
            this.isVisible = true;
            this.updateProgress(); // 立即更新一次
        }
    }
}

// 初始化浮动组件
let floatWidget;

document.addEventListener('DOMContentLoaded', () => {
    floatWidget = new FloatWidget();
    window.floatWidget = floatWidget; // 全局访问
});

// 窗口关闭时清理
window.addEventListener('beforeunload', () => {
    if (floatWidget) {
        floatWidget.cleanup();
    }
});
