// 修复编码和同步问题的脚本
// 在主窗口控制台运行此脚本

console.log('=== 开始修复编码和同步问题 ===');

// 1. 检查当前数据状态
async function checkCurrentData() {
    console.log('1. 检查当前数据状态...');
    
    try {
        // 获取今日数据
        const todayKey = 'watertime_2025_7_24';
        const todayData = await window.electronAPI.getStoreData(todayKey);
        console.log('今日数据:', todayData);
        
        // 获取设置
        const settings = await window.electronAPI.getStoreData('settings');
        console.log('设置:', settings);
        
        return { todayData, settings };
    } catch (error) {
        console.error('获取数据失败:', error);
        return null;
    }
}

// 2. 强制同步数据到浮窗
async function forceSyncToFloat() {
    console.log('2. 强制同步数据到浮窗...');
    
    try {
        const todayKey = 'watertime_2025_7_24';
        const todayData = await window.electronAPI.getStoreData(todayKey);
        
        if (todayData) {
            // 直接通过IPC发送数据更新
            await window.electronAPI.setStoreData(todayKey, todayData);
            console.log('数据同步完成:', todayData.total, 'ml');
            return true;
        } else {
            console.error('没有找到今日数据');
            return false;
        }
    } catch (error) {
        console.error('同步失败:', error);
        return false;
    }
}

// 3. 测试浮窗点击功能
function testFloatClick() {
    console.log('3. 测试浮窗点击功能...');
    
    // 这个需要在浮窗中运行
    console.log('请在浮窗开发者工具中运行以下代码:');
    console.log(`
// 测试点击功能
if (window.floatWidget) {
    console.log('浮窗实例存在');
    console.log('当前数据:', window.floatWidget.data);
    
    // 模拟点击
    const widget = document.getElementById('floatWidget');
    if (widget) {
        widget.click();
        console.log('点击事件已触发');
    }
} else {
    console.error('浮窗实例不存在');
}
    `);
}

// 4. 修复数据管理器
async function fixDataManager() {
    console.log('4. 修复数据管理器...');
    
    if (window.dataManager) {
        try {
            // 清除缓存
            window.dataManager.cache.clear();
            console.log('缓存已清除');
            
            // 重新初始化
            await window.dataManager.initialize();
            console.log('数据管理器重新初始化完成');
            
            return true;
        } catch (error) {
            console.error('修复数据管理器失败:', error);
            return false;
        }
    } else {
        console.error('数据管理器不存在');
        return false;
    }
}

// 5. 创建简化的数据同步函数
function createSimpleSync() {
    console.log('5. 创建简化的数据同步函数...');
    
    window.simpleSync = async function() {
        try {
            const todayKey = 'watertime_2025_7_24';
            const data = await window.electronAPI.getStoreData(todayKey);
            
            if (data) {
                console.log('主窗口数据:', data.total, 'ml');
                
                // 强制保存数据触发同步
                await window.electronAPI.setStoreData(todayKey, {
                    ...data,
                    updated: new Date().toISOString()
                });
                
                console.log('数据同步信号已发送');
                return data;
            }
        } catch (error) {
            console.error('简单同步失败:', error);
        }
    };
    
    console.log('简化同步函数已创建，使用 window.simpleSync() 调用');
}

// 6. 创建浮窗测试函数
function createFloatTest() {
    console.log('6. 创建浮窗测试函数...');
    
    window.testFloat = function() {
        console.log('请将以下代码复制到浮窗控制台运行:');
        console.log(`
// === 浮窗测试代码 ===
console.log('=== 浮窗诊断开始 ===');

// 检查基本组件
console.log('1. 检查基本组件:');
console.log('dataManager:', window.dataManager ? '✓' : '✗');
console.log('floatWidget:', window.floatWidget ? '✓' : '✗');
console.log('electronAPI:', window.electronAPI ? '✓' : '✗');

// 检查DOM元素
console.log('2. 检查DOM元素:');
const elements = ['floatWidget', 'floatPercentage', 'floatAmount', 'progressArc'];
elements.forEach(id => {
    const el = document.getElementById(id);
    console.log(id + ':', el ? '✓' : '✗');
});

// 获取当前数据
console.log('3. 获取当前数据:');
if (window.dataManager) {
    window.dataManager.getTodayData().then(data => {
        console.log('今日数据:', data);
        if (window.floatWidget) {
            window.floatWidget.data.todayTotal = data.total || 0;
            window.floatWidget.updateProgress();
            console.log('数据已更新并刷新显示');
        }
    }).catch(err => console.error('获取数据失败:', err));
}

// 测试点击
console.log('4. 测试点击:');
const widget = document.getElementById('floatWidget');
if (widget) {
    widget.addEventListener('click', () => {
        console.log('点击事件触发!');
    });
    console.log('点击监听器已添加，请点击浮窗测试');
}

console.log('=== 浮窗诊断完成 ===');
        `);
    };
    
    console.log('浮窗测试函数已创建，使用 window.testFloat() 获取测试代码');
}

// 执行修复
async function runFix() {
    console.log('开始执行修复流程...\n');
    
    // 检查数据
    const data = await checkCurrentData();
    if (!data) {
        console.error('无法获取基础数据，修复中止');
        return;
    }
    
    // 修复数据管理器
    await fixDataManager();
    
    // 强制同步
    await forceSyncToFloat();
    
    // 创建辅助函数
    createSimpleSync();
    createFloatTest();
    
    console.log('\n=== 修复完成 ===');
    console.log('请执行以下步骤:');
    console.log('1. 运行 window.simpleSync() 强制同步数据');
    console.log('2. 运行 window.testFloat() 获取浮窗测试代码');
    console.log('3. 在浮窗开发者工具中运行测试代码');
    console.log('4. 测试点击和右键功能');
}

// 自动运行修复
runFix();
