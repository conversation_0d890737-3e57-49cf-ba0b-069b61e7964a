// 测试修复效果的脚本
// 在浮窗开发者工具中运行

console.log('=== 测试修复效果 ===');

// 1. 测试数据管理器
async function testDataManager() {
    console.log('1. 测试数据管理器...');
    
    if (!window.dataManager) {
        console.error('❌ 数据管理器不存在');
        return false;
    }
    
    try {
        // 测试获取今日数据
        const todayData = await window.dataManager.getTodayData();
        console.log('✅ 今日数据获取成功:', todayData);
        
        // 测试获取设置
        const settings = await window.dataManager.getSettings();
        console.log('✅ 设置获取成功:', settings);
        
        return true;
    } catch (error) {
        console.error('❌ 数据管理器测试失败:', error);
        return false;
    }
}

// 2. 测试浮窗实例
function testFloatWidget() {
    console.log('2. 测试浮窗实例...');
    
    if (!window.floatWidget) {
        console.error('❌ 浮窗实例不存在');
        return false;
    }
    
    console.log('✅ 浮窗实例存在');
    console.log('当前数据:', window.floatWidget.data);
    
    return true;
}

// 3. 测试DOM元素
function testDOMElements() {
    console.log('3. 测试DOM元素...');
    
    const elements = {
        floatWidget: document.getElementById('floatWidget'),
        floatPercentage: document.getElementById('floatPercentage'),
        floatAmount: document.getElementById('floatAmount'),
        progressArc: document.getElementById('progressArc')
    };
    
    let allGood = true;
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name} 存在`);
        } else {
            console.error(`❌ ${name} 不存在`);
            allGood = false;
        }
    });
    
    return allGood;
}

// 4. 测试点击事件
function testClickEvent() {
    console.log('4. 测试点击事件...');
    
    const widget = document.getElementById('floatWidget');
    if (!widget) {
        console.error('❌ 找不到浮窗元素');
        return false;
    }
    
    // 检查事件监听器
    const hasClickListener = widget.onclick !== null || 
                            widget.addEventListener !== undefined;
    
    if (hasClickListener) {
        console.log('✅ 点击事件监听器已设置');
        
        // 模拟点击
        console.log('模拟点击...');
        widget.click();
        
        return true;
    } else {
        console.error('❌ 点击事件监听器未设置');
        return false;
    }
}

// 5. 测试右键菜单
function testContextMenu() {
    console.log('5. 测试右键菜单...');
    
    const widget = document.getElementById('floatWidget');
    const contextMenu = document.getElementById('contextMenu');
    
    if (!widget || !contextMenu) {
        console.error('❌ 右键菜单元素不完整');
        return false;
    }
    
    console.log('✅ 右键菜单元素存在');
    
    // 模拟右键
    console.log('模拟右键点击...');
    const event = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
        clientX: 60,
        clientY: 60
    });
    widget.dispatchEvent(event);
    
    return true;
}

// 6. 测试数据同步
async function testDataSync() {
    console.log('6. 测试数据同步...');
    
    if (!window.floatWidget || !window.dataManager) {
        console.error('❌ 必要组件不存在');
        return false;
    }
    
    try {
        // 强制同步数据
        await window.floatWidget.forceSyncData();
        console.log('✅ 数据同步成功');
        
        // 检查数据是否更新
        const currentData = window.floatWidget.data;
        console.log('同步后数据:', currentData);
        
        return true;
    } catch (error) {
        console.error('❌ 数据同步失败:', error);
        return false;
    }
}

// 7. 测试动画性能
function testAnimation() {
    console.log('7. 测试动画性能...');
    
    if (!window.floatWidget) {
        console.error('❌ 浮窗实例不存在');
        return false;
    }
    
    try {
        // 触发进度更新动画
        window.floatWidget.updateProgress();
        console.log('✅ 动画触发成功');
        
        return true;
    } catch (error) {
        console.error('❌ 动画测试失败:', error);
        return false;
    }
}

// 执行所有测试
async function runAllTests() {
    console.log('开始执行所有测试...\n');
    
    const tests = [
        { name: '数据管理器', fn: testDataManager },
        { name: '浮窗实例', fn: testFloatWidget },
        { name: 'DOM元素', fn: testDOMElements },
        { name: '点击事件', fn: testClickEvent },
        { name: '右键菜单', fn: testContextMenu },
        { name: '数据同步', fn: testDataSync },
        { name: '动画性能', fn: testAnimation }
    ];
    
    let passedCount = 0;
    
    for (const test of tests) {
        console.log(`\n--- 测试: ${test.name} ---`);
        try {
            const result = await test.fn();
            if (result) {
                passedCount++;
                console.log(`✅ ${test.name} 测试通过`);
            } else {
                console.log(`❌ ${test.name} 测试失败`);
            }
        } catch (error) {
            console.error(`❌ ${test.name} 测试异常:`, error);
        }
    }
    
    console.log(`\n=== 测试完成 ===`);
    console.log(`通过: ${passedCount}/${tests.length}`);
    
    if (passedCount === tests.length) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步修复');
    }
}

// 自动运行测试
runAllTests();
