const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u, ipcMain, nativeImage, screen } = require('electron');
const path = require('path');
const fs = require('fs');

// 开发环境检测
const isDev = process.argv.includes('--dev');

// 应用状态管理
let appState = {
  isQuiting: false,
  isReady: false,
  windows: {
    main: null,
    float: null
  },
  tray: null,
  lastActiveWindow: null
};

// 日志记录
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  console.log(logMessage);

  if (isDev) {
    // 开发环境下可以添加更详细的日志
    if (level === 'error') {
      console.trace();
    }
  }
}

// 简单的数据存储 (替代 electron-store)
const dataPath = path.join(__dirname, 'data.json');
const store = {
  get: (key) => {
    try {
      const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
      return data[key];
    } catch (error) {
      return null;
    }
  },
  set: (key, value) => {
    try {
      let data = {};
      try {
        data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
      } catch (error) {
        // 文件不存在或格式错误，使用空对象
      }
      data[key] = value;
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }
};

// 全局变量
let mainWindow = null;
let floatWindow = null;
let tray = null;

// 应用配置
const config = {
  mainWindow: {
    width: 400,
    height: 750,  // 增加高度以容纳所有内容
    minWidth: 350,
    minHeight: 650  // 相应增加最小高度
  },
  floatWindow: {
    width: 80,   // 缩小到80px，紧贴实际圆形内容，避免透明边框阻挡点击
    height: 80   // 缩小到80px，紧贴实际圆形内容，避免透明边框阻挡点击
  }
};

// ==================== 应用生命周期管理 ====================

// 防止多实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  log('App already running, quit current instance', 'info');
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    log('Second instance detected, focus main window', 'info');
    // 当运行第二个实例时，将会聚焦到主窗口
    if (appState.windows.main) {
      if (appState.windows.main.isMinimized()) {
        appState.windows.main.restore();
      }
      appState.windows.main.focus();
      appState.windows.main.show();
    }
  });
}

// 应用启动事件
app.on('before-quit', (event) => {
  log('应用准备退出', 'info');
  appState.isQuiting = true;
});

app.on('will-quit', (event) => {
  log('应用即将退出', 'info');
  // 可以在这里做一些清理工作
  saveAppState();
});

app.on('quit', () => {
  log('应用已退出', 'info');
});

// 应用激活事件 (macOS)
app.on('activate', () => {
  log('应用被激活', 'info');
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  } else if (appState.windows.main) {
    appState.windows.main.show();
  }
});

// 所有窗口关闭事件
app.on('window-all-closed', () => {
  log('所有窗口已关闭', 'info');
  // 在 Windows 和 Linux 上，保持应用运行（托盘模式）
  // 在 macOS 上，通常会退出应用
  if (process.platform === 'darwin') {
    app.quit();
  }
  // 其他平台保持运行，通过托盘管理
});

// 应用准备就绪
app.whenReady().then(() => {
  log('应用准备就绪，开始初始化', 'info');
  appState.isReady = true;

  initializeApp();
}).catch((error) => {
  log(`应用初始化失败: ${error.message}`, 'error');
});

// 初始化应用
async function initializeApp() {
  try {
    // 恢复应用状态
    await restoreAppState();

    // 创建窗口
    await createMainWindow();
    await createFloatWindow();

    // 创建系统托盘
    await createTray();

    // 设置全局快捷键
    setupGlobalShortcuts();

    log('应用初始化完成', 'info');
  } catch (error) {
    log(`应用初始化过程中出错: ${error.message}`, 'error');
  }
}

// ==================== 窗口创建和管理 ====================

// 创建主窗口
async function createMainWindow() {
  if (appState.windows.main) {
    log('主窗口已存在，跳过创建', 'warn');
    return appState.windows.main;
  }

  log('创建主窗口', 'info');

  try {
    appState.windows.main = new BrowserWindow({
      width: config.mainWindow.width,
      height: config.mainWindow.height,
      minWidth: config.mainWindow.minWidth,
      minHeight: config.mainWindow.minHeight,
      show: false, // 初始不显示
      icon: path.join(__dirname, 'app/assets/icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        enableRemoteModule: false,
        sandbox: false
      },
      titleBarStyle: 'default',
      autoHideMenuBar: true, // 隐藏菜单栏
      frame: true,
      resizable: true,
      maximizable: true,
      minimizable: true,
      closable: true
    });

    // 设置窗口标题
    appState.windows.main.setTitle('WaterTime - 饮水记录助手');

    // 加载主页面
    await appState.windows.main.loadFile(path.join(__dirname, 'app/index.html'));
    log('主窗口页面加载完成', 'info');

    // 开发环境下可以通过快捷键打开开发者工具
    if (isDev) {
      // 不自动打开开发者工具，避免界面混乱
      // 可以通过 Ctrl+Shift+I 或 F12 手动打开
      log('开发环境：可通过 Ctrl+Shift+I 打开开发者工具', 'info');
    }

    // 窗口事件处理
    setupMainWindowEvents();

    // 恢复窗口位置和大小
    restoreWindowBounds();

    // 窗口准备好后显示
    appState.windows.main.once('ready-to-show', () => {
      log('主窗口准备就绪', 'info');
      // 显示主窗口
      appState.windows.main.show();
      appState.windows.main.focus();
    });

    return appState.windows.main;
  } catch (error) {
    log(`创建主窗口失败: ${error.message}`, 'error');
    throw error;
  }
}

// 设置主窗口事件
function setupMainWindowEvents() {
  const mainWindow = appState.windows.main;

  // 窗口关闭时隐藏到托盘，而不是退出
  mainWindow.on('close', (event) => {
    if (!appState.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
      log('主窗口已隐藏到托盘', 'info');

      // 显示托盘提示
      showTrayNotification('WaterTime', '应用已最小化到系统托盘');
    }
  });

  // 窗口最小化
  mainWindow.on('minimize', () => {
    log('主窗口已最小化', 'info');
  });

  // 窗口恢复
  mainWindow.on('restore', () => {
    log('主窗口已恢复', 'info');
  });

  // 窗口聚焦
  mainWindow.on('focus', () => {
    appState.lastActiveWindow = 'main';
  });

  // 记住窗口位置和大小
  mainWindow.on('moved', saveWindowBounds);
  mainWindow.on('resized', saveWindowBounds);

  // 窗口销毁
  mainWindow.on('closed', () => {
    log('主窗口已销毁', 'info');
    appState.windows.main = null;
  });
}

// 创建浮动窗口
async function createFloatWindow() {
  if (appState.windows.float) {
    log('浮动窗口已存在，跳过创建', 'warn');
    return appState.windows.float;
  }

  log('创建浮动窗口', 'info');

  try {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;

    // 获取保存的浮动窗口位置
    const savedPosition = store.get('floatPosition') || {
      x: width - config.floatWindow.width - 20,
      y: height - config.floatWindow.height - 20
    };

    appState.windows.float = new BrowserWindow({
      width: config.floatWindow.width,
      height: config.floatWindow.height,
      x: savedPosition.x,
      y: savedPosition.y,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      transparent: true,
      show: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        enableRemoteModule: false,
        sandbox: false,
        // 优化渲染性能
        experimentalFeatures: true,
        offscreen: false
      },
      // 移除 type: 'toolbar'，使用默认窗口类型
      focusable: true,
      minimizable: false,
      maximizable: false,
      closable: false,
      // 简化配置，移除可能导致交互问题的选项
      hasShadow: false,
      titleBarStyle: 'hidden'
    });

    // 加载浮动窗口页面
    await appState.windows.float.loadFile('app/float.html');
    log('浮动窗口页面加载完成', 'info');

    // 开发环境下可以通过快捷键打开开发者工具
    if (isDev) {
      // 不自动打开浮动窗口的开发者工具，避免干扰
      // 可以通过右键菜单或快捷键手动打开
      log('开发环境：浮动窗口可通过右键菜单打开开发者工具', 'info');
    }

    // 设置浮动窗口事件
    setupFloatWindowEvents();

    return appState.windows.float;
  } catch (error) {
    log(`创建浮动窗口失败: ${error.message}`, 'error');
    throw error;
  }
}

// 设置浮动窗口事件
function setupFloatWindowEvents() {
  const floatWindow = appState.windows.float;

  // 浮动窗口不应该被关闭，只能隐藏
  floatWindow.on('close', (event) => {
    event.preventDefault();
    floatWindow.hide();
    log('浮动窗口已隐藏', 'info');
  });

  // 窗口移动时保存位置
  floatWindow.on('moved', () => {
    const position = floatWindow.getPosition();
    store.set('floatPosition', { x: position[0], y: position[1] });
  });

  // 窗口聚焦
  floatWindow.on('focus', () => {
    appState.lastActiveWindow = 'float';
  });

  // 窗口销毁
  floatWindow.on('closed', () => {
    log('浮动窗口已销毁', 'info');
    appState.windows.float = null;
  });
}

// ==================== 系统托盘功能 ====================

// 创建系统托盘
async function createTray() {
  if (appState.tray) {
    log('系统托盘已存在，跳过创建', 'warn');
    return appState.tray;
  }

  log('创建系统托盘', 'info');

  try {
    // 创建托盘图标
    const iconPath = path.join(__dirname, 'app/assets/tray-icon.png');

    // 检查图标文件是否存在
    if (!fs.existsSync(iconPath)) {
      log(`托盘图标文件不存在: ${iconPath}`, 'warn');
      // 使用默认图标
      const defaultIconPath = path.join(__dirname, 'app/assets/icon16.png');
      if (fs.existsSync(defaultIconPath)) {
        log('使用默认图标', 'info');
      }
    }

    const trayIcon = nativeImage.createFromPath(iconPath);
    appState.tray = new Tray(trayIcon);

    // 设置托盘提示
    appState.tray.setToolTip('WaterTime - 饮水记录助手');

    // 创建托盘菜单
    updateTrayMenu();

    // 设置托盘事件
    setupTrayEvents();

    log('系统托盘创建完成', 'info');
    return appState.tray;
  } catch (error) {
    log(`创建系统托盘失败: ${error.message}`, 'error');
    throw error;
  }
}

// 更新托盘菜单
function updateTrayMenu() {
  if (!appState.tray) return;

  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示主窗口',
      click: () => {
        showMainWindow();
      }
    },
    {
      label: appState.windows.float && appState.windows.float.isVisible() ? '隐藏浮动圆圈' : '显示浮动圆圈',
      click: () => {
        toggleFloatWindow();
      }
    },
    { type: 'separator' },
    {
      label: '快速记录饮水',
      click: () => {
        // 通过 IPC 发送快速记录命令
        if (appState.windows.main) {
          appState.windows.main.webContents.send('quick-drink');
        }
      }
    },
    { type: 'separator' },
    {
      label: '设置',
      submenu: [
        {
          label: '开机自启动',
          type: 'checkbox',
          checked: app.getLoginItemSettings().openAtLogin,
          click: (menuItem) => {
            setAutoStartup(menuItem.checked);
          }
        },
        {
          label: '显示浮动圆圈',
          type: 'checkbox',
          checked: appState.windows.float && appState.windows.float.isVisible(),
          click: (menuItem) => {
            if (menuItem.checked) {
              showFloatWindow();
            } else {
              hideFloatWindow();
            }
          }
        }
      ]
    },
    { type: 'separator' },
    {
      label: '关于',
      click: () => {
        showAboutDialog();
      }
    },
    {
      label: '退出 WaterTime',
      click: () => {
        quitApp();
      }
    }
  ]);

  appState.tray.setContextMenu(contextMenu);
}

// 设置托盘事件
function setupTrayEvents() {
  if (!appState.tray) return;

  // 左键点击托盘图标显示主窗口
  appState.tray.on('click', () => {
    log('托盘图标被点击', 'info');
    showMainWindow();
  });

  // 双击托盘图标显示主窗口
  appState.tray.on('double-click', () => {
    log('托盘图标被双击', 'info');
    showMainWindow();
  });

  // 右键点击显示菜单（某些系统需要）
  appState.tray.on('right-click', () => {
    log('托盘图标被右键点击', 'info');
    appState.tray.popUpContextMenu();
  });
}

// 显示托盘通知
function showTrayNotification(title, content, options = {}) {
  if (!appState.tray) return;

  try {
    appState.tray.displayBalloon({
      iconType: options.iconType || 'info',
      title: title,
      content: content,
      ...options
    });
    log(`显示托盘通知: ${title} - ${content}`, 'info');
  } catch (error) {
    log(`显示托盘通知失败: ${error.message}`, 'warn');
  }
}

// ==================== 窗口管理辅助功能 ====================

// 显示主窗口
function showMainWindow() {
  if (!appState.windows.main) {
    log('主窗口不存在，创建新窗口', 'warn');
    createMainWindow();
    return;
  }

  try {
    if (appState.windows.main.isMinimized()) {
      appState.windows.main.restore();
    }
    appState.windows.main.show();
    appState.windows.main.focus();
    log('主窗口已显示', 'info');
  } catch (error) {
    log(`显示主窗口失败: ${error.message}`, 'error');
  }
}

// 隐藏主窗口
function hideMainWindow() {
  if (appState.windows.main) {
    appState.windows.main.hide();
    log('主窗口已隐藏', 'info');
  }
}

// 显示浮动窗口
function showFloatWindow() {
  if (!appState.windows.float) {
    createFloatWindow();
    return;
  }

  appState.windows.float.show();
  log('浮动窗口已显示', 'info');
  updateTrayMenu();
}

// 隐藏浮动窗口
function hideFloatWindow() {
  if (appState.windows.float) {
    appState.windows.float.hide();
    log('浮动窗口已隐藏', 'info');
    updateTrayMenu();
  }
}

// 切换浮动窗口显示状态
function toggleFloatWindow() {
  if (!appState.windows.float) {
    createFloatWindow();
    return;
  }

  if (appState.windows.float.isVisible()) {
    hideFloatWindow();
  } else {
    showFloatWindow();
  }
}

// 保存窗口位置和大小
function saveWindowBounds() {
  if (!appState.windows.main) return;

  try {
    const bounds = appState.windows.main.getBounds();
    store.set('windowBounds', bounds);
    log('窗口位置已保存', 'debug');
  } catch (error) {
    log(`保存窗口位置失败: ${error.message}`, 'warn');
  }
}

// 恢复窗口位置和大小
function restoreWindowBounds() {
  if (!appState.windows.main) return;

  try {
    const bounds = store.get('windowBounds');
    if (bounds) {
      // 验证位置是否在屏幕范围内
      const displays = screen.getAllDisplays();
      const isValidPosition = displays.some(display => {
        const area = display.workArea;
        return bounds.x >= area.x && bounds.y >= area.y &&
               bounds.x < area.x + area.width && bounds.y < area.y + area.height;
      });

      if (isValidPosition) {
        appState.windows.main.setBounds(bounds);
        log('窗口位置已恢复', 'info');
      } else {
        log('保存的窗口位置无效，使用默认位置', 'warn');
      }
    }
  } catch (error) {
    log(`恢复窗口位置失败: ${error.message}`, 'warn');
  }
}

// ==================== 系统功能 ====================

// 设置开机自启动
function setAutoStartup(enabled) {
  try {
    app.setLoginItemSettings({
      openAtLogin: enabled,
      openAsHidden: true // 启动时隐藏窗口
    });

    store.set('autoStartup', enabled);
    log(`开机自启动已${enabled ? '启用' : '禁用'}`, 'info');
    updateTrayMenu();
    return true;
  } catch (error) {
    log(`设置开机自启动失败: ${error.message}`, 'error');
    return false;
  }
}

// 设置全局快捷键
function setupGlobalShortcuts() {
  const { globalShortcut } = require('electron');

  try {
    // 快速显示/隐藏主窗口
    globalShortcut.register('CommandOrControl+Shift+W', () => {
      log('全局快捷键触发: 显示/隐藏主窗口', 'info');
      if (appState.windows.main && appState.windows.main.isVisible()) {
        hideMainWindow();
      } else {
        showMainWindow();
      }
    });

    // 快速记录饮水
    globalShortcut.register('CommandOrControl+Shift+D', () => {
      log('全局快捷键触发: 快速记录饮水', 'info');
      if (appState.windows.main) {
        appState.windows.main.webContents.send('quick-drink');
      }
    });

    log('全局快捷键设置完成', 'info');
  } catch (error) {
    log(`设置全局快捷键失败: ${error.message}`, 'warn');
  }
}

// 显示关于对话框
function showAboutDialog() {
  const { dialog } = require('electron');

  dialog.showMessageBox(appState.windows.main || null, {
    type: 'info',
    title: '关于 WaterTime',
    message: 'WaterTime 桌面版',
    detail: `版本: ${app.getVersion()}\n一个简洁美观的饮水记录助手\n\n© 2025 WaterTime Team`,
    buttons: ['确定']
  });
}

// 退出应用
function quitApp() {
  log('用户请求退出应用', 'info');
  appState.isQuiting = true;

  // 清理全局快捷键
  const { globalShortcut } = require('electron');
  globalShortcut.unregisterAll();

  app.quit();
}

// 保存应用状态
function saveAppState() {
  try {
    const state = {
      lastActiveWindow: appState.lastActiveWindow,
      floatWindowVisible: appState.windows.float ? appState.windows.float.isVisible() : true,
      timestamp: Date.now()
    };

    store.set('appState', state);
    log('应用状态已保存', 'info');
  } catch (error) {
    log(`保存应用状态失败: ${error.message}`, 'warn');
  }
}

// 恢复应用状态
async function restoreAppState() {
  try {
    const state = store.get('appState');
    if (state) {
      appState.lastActiveWindow = state.lastActiveWindow || 'main';
      log('应用状态已恢复', 'info');
    }
  } catch (error) {
    log(`恢复应用状态失败: ${error.message}`, 'warn');
  }
}

// 在应用初始化时调用 IPC 设置
app.whenReady().then(() => {
  setupIpcHandlers();
});

// ==================== 进程间通信 (IPC) ====================

// 设置 IPC 处理器
function setupIpcHandlers() {
  log('设置 IPC 处理器', 'info');

  // 数据存储相关
  ipcMain.handle('get-store-data', async (event, key) => {
    try {
      const data = store.get(key);
      log(`获取数据: ${key}`, 'debug');
      return data;
    } catch (error) {
      log(`获取数据失败: ${key} - ${error.message}`, 'error');
      return null;
    }
  });

  ipcMain.handle('set-store-data', async (event, key, value) => {
    try {
      store.set(key, value);
      log(`保存数据: ${key}`, 'debug');

      // 简化：不再广播，让各窗口自己定时刷新数据
      // broadcastDataUpdate(key, value); // 移除复杂的广播机制
      return true;
    } catch (error) {
      log(`保存数据失败: ${key} - ${error.message}`, 'error');
      return false;
    }
  });

  // 应用信息相关
  ipcMain.handle('get-app-version', () => {
    return app.getVersion();
  });

  ipcMain.handle('get-app-info', () => {
    return {
      version: app.getVersion(),
      name: app.getName(),
      isPackaged: app.isPackaged,
      isDev: isDev
    };
  });

  // 窗口控制相关
  ipcMain.handle('minimize-window', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      window.minimize();
    }
  });

  ipcMain.handle('close-window', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender);
    if (window) {
      window.close();
    }
  });

  ipcMain.handle('show-main-window', () => {
    showMainWindow();
  });

  // 浮动窗口相关
  // 简化的位置更新：立即更新，延迟保存
  let positionSaveTimer = null;

  ipcMain.handle('update-float-position', (event, x, y) => {
    try {
      if (appState.windows.float) {
        // 立即更新窗口位置（同步操作）
        appState.windows.float.setPosition(Math.round(x), Math.round(y));

        // 延迟保存位置信息，避免频繁写入
        if (positionSaveTimer) {
          clearTimeout(positionSaveTimer);
        }

        positionSaveTimer = setTimeout(() => {
          store.set('floatPosition', { x: Math.round(x), y: Math.round(y) });
          log(`保存浮动窗口位置: (${Math.round(x)}, ${Math.round(y)})`, 'debug');
        }, 200); // 200ms 后保存位置
      }
      return true;
    } catch (error) {
      log(`更新浮动窗口位置失败: ${error.message}`, 'error');
      return false;
    }
  });

  ipcMain.handle('toggle-float-window', () => {
    toggleFloatWindow();
  });

  // 数据同步相关
  ipcMain.handle('sync-data-to-float', async () => {
    try {
      if (appState.windows.float && !appState.windows.float.isDestroyed()) {
        // 向浮窗发送数据刷新消息
        appState.windows.float.webContents.send('force-data-refresh');
        log('已通知浮窗刷新数据', 'debug');
        return true;
      }
      return false;
    } catch (error) {
      log(`同步数据到浮窗失败: ${error.message}`, 'error');
      return false;
    }
  });

  // 系统功能相关
  ipcMain.handle('set-auto-startup', (event, enabled) => {
    return setAutoStartup(enabled);
  });

  ipcMain.handle('get-auto-startup', () => {
    return app.getLoginItemSettings().openAtLogin;
  });

  // 通知相关
  ipcMain.handle('show-notification', (event, title, content, options = {}) => {
    showTrayNotification(title, content, options);
  });

  // 快速操作
  ipcMain.handle('quick-drink', (event, amount) => {
    // 处理快速饮水记录
    log(`快速记录饮水: ${amount}ml`, 'info');
    // 这里可以添加具体的业务逻辑
    return true;
  });
}

// 广播数据更新到所有窗口
function broadcastDataUpdate(key, value) {
  const windows = [appState.windows.main, appState.windows.float];

  windows.forEach(window => {
    if (window && !window.isDestroyed()) {
      try {
        window.webContents.send('data-update', { key, value });
      } catch (error) {
        log(`广播数据更新失败: ${error.message}`, 'warn');
      }
    }
  });
}
