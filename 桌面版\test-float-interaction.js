// 浮窗交互测试脚本
console.log('🎯 === 浮窗交互测试 ===');

// 检查DOM元素
function checkFloatDOM() {
    console.log('1. 检查浮窗DOM元素:');
    
    const container = document.getElementById('floatContainer');
    const widget = document.getElementById('floatWidget');
    const dragHandle = document.getElementById('dragHandle');
    
    console.log('floatContainer:', container ? '✅ 存在' : '❌ 不存在');
    console.log('floatWidget:', widget ? '✅ 存在' : '❌ 不存在');
    console.log('dragHandle:', dragHandle ? '✅ 存在' : '❌ 不存在');
    
    if (widget) {
        const styles = getComputedStyle(widget);
        console.log('widget样式检查:');
        console.log('- pointer-events:', styles.pointerEvents);
        console.log('- cursor:', styles.cursor);
        console.log('- background:', styles.background);
        console.log('- transform:', styles.transform);
        console.log('- transition:', styles.transition);
    }
    
    if (container) {
        const containerStyles = getComputedStyle(container);
        console.log('container样式检查:');
        console.log('- pointer-events:', containerStyles.pointerEvents);
    }
}

// 检查事件监听器
function checkEventListeners() {
    console.log('2. 检查事件监听器:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        // 手动触发事件测试
        console.log('触发mouseenter事件...');
        widget.dispatchEvent(new MouseEvent('mouseenter'));
        
        setTimeout(() => {
            console.log('触发mouseleave事件...');
            widget.dispatchEvent(new MouseEvent('mouseleave'));
        }, 1000);
        
        setTimeout(() => {
            console.log('触发click事件...');
            widget.dispatchEvent(new MouseEvent('click'));
        }, 2000);
    }
}

// 检查JavaScript对象
function checkFloatWidget() {
    console.log('3. 检查FloatWidget对象:');
    
    console.log('window.floatWidget:', window.floatWidget ? '✅ 存在' : '❌ 不存在');
    
    if (window.floatWidget) {
        console.log('FloatWidget状态:');
        console.log('- dataManager:', window.floatWidget.dataManager ? '✅ 存在' : '❌ 不存在');
        console.log('- isDragging:', window.floatWidget.isDragging);
        console.log('- isProcessing:', window.floatWidget.isProcessing);
        console.log('- data:', window.floatWidget.data);
        
        // 检查事件处理器
        console.log('事件处理器:');
        console.log('- basicMouseEnterHandler:', typeof window.floatWidget.basicMouseEnterHandler);
        console.log('- basicMouseLeaveHandler:', typeof window.floatWidget.basicMouseLeaveHandler);
        console.log('- handleDirectClick:', typeof window.floatWidget.handleDirectClick);
    }
}

// 测试拖拽功能
function testDrag() {
    console.log('4. 测试拖拽功能:');
    
    const dragHandle = document.getElementById('dragHandle');
    if (dragHandle) {
        console.log('模拟拖拽开始...');
        
        // 模拟鼠标按下
        const mouseDown = new MouseEvent('mousedown', {
            clientX: 100,
            clientY: 100,
            bubbles: true
        });
        dragHandle.dispatchEvent(mouseDown);
        
        setTimeout(() => {
            // 模拟鼠标移动
            const mouseMove = new MouseEvent('mousemove', {
                clientX: 150,
                clientY: 150,
                bubbles: true
            });
            document.dispatchEvent(mouseMove);
            
            setTimeout(() => {
                // 模拟鼠标释放
                const mouseUp = new MouseEvent('mouseup', {
                    bubbles: true
                });
                document.dispatchEvent(mouseUp);
                console.log('拖拽测试完成');
            }, 500);
        }, 500);
    }
}

// 测试样式动画
function testAnimations() {
    console.log('5. 测试样式动画:');
    
    const widget = document.getElementById('floatWidget');
    if (widget) {
        console.log('测试悬浮动画...');
        
        // 手动设置悬浮样式
        widget.style.transform = 'scale(1.08) translateY(-2px)';
        widget.style.boxShadow = '0 8px 25px rgba(0, 191, 255, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15)';
        widget.style.filter = 'brightness(1.1)';
        
        setTimeout(() => {
            console.log('恢复正常样式...');
            widget.style.transform = 'scale(1) translateY(0)';
            widget.style.boxShadow = '0 4px 12px rgba(0, 191, 255, 0.2), 0 2px 6px rgba(0, 0, 0, 0.1)';
            widget.style.filter = 'brightness(1)';
        }, 2000);
    }
}

// 运行所有测试
function runAllTests() {
    console.log('🧪 开始浮窗交互测试...');
    
    checkFloatDOM();
    checkEventListeners();
    checkFloatWidget();
    
    setTimeout(() => testDrag(), 3000);
    setTimeout(() => testAnimations(), 6000);
    
    console.log('📝 测试进行中，请观察浮窗的反应...');
}

// 导出测试函数
window.floatTest = {
    checkFloatDOM,
    checkEventListeners,
    checkFloatWidget,
    testDrag,
    testAnimations,
    runAllTests
};

console.log('浮窗交互测试脚本加载完成！');
console.log('运行 floatTest.runAllTests() 开始测试');
