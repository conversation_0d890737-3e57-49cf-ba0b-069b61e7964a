// WaterTime 浮动圆圈 Content Script

// 防止重复注入
if (window.watertimeInjected) {
    console.log('WaterTime 已注入，跳过');
} else {
    window.watertimeInjected = true;
    
    // 应用状态
    let watertimeData = {
        dailyTarget: 2000,
        singleDrink: 200,
        todayTotal: 0,
        todayRecords: []
    };

    let watertimeSettings = {
        floatPosition: 'bottom-right',
        circleSize: 'medium',
        enableAnimations: true
    };

    let watertimeState = {
        isProcessing: false,
        lastClickTime: 0,
        widget: null,
        progressArc: null,
        percentageElement: null,
        amountElement: null,
        // 拖拽状态
        isDragging: false,
        dragOffset: { x: 0, y: 0 },
        lastPosition: { x: 0, y: 0 },
        startPosition: { x: 0, y: 0 },
        dragStartTime: 0,
        dragDuration: 0
    };
    
    // 初始化浮动圆圈
    function initWaterTimeWidget() {
        console.log('初始化 WaterTime 浮动圆圈...');
        
        // 创建浮动容器
        const widget = document.createElement('div');
        widget.id = 'watertime-float-widget';
        widget.innerHTML = `
            <div class="watertime-progress-container">
                <svg class="watertime-progress-circle" viewBox="0 0 120 120">
                    <circle class="watertime-progress-bg" cx="60" cy="60" r="52"></circle>
                    <circle class="watertime-progress-arc" cx="60" cy="60" r="52"
                            stroke-dasharray="326.7" stroke-dashoffset="326.7"></circle>
                </svg>
                <div class="watertime-progress-content">
                    <div class="watertime-percentage">0%</div>
                    <div class="watertime-amount">0ml</div>
                    <div class="watertime-water-icon"></div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(widget);
        
        // 保存引用
        watertimeState.widget = widget;
        watertimeState.progressArc = widget.querySelector('.watertime-progress-arc');
        watertimeState.percentageElement = widget.querySelector('.watertime-percentage');
        watertimeState.amountElement = widget.querySelector('.watertime-amount');

        // 设置图标 - 使用自定义SVG图标
        const iconElement = widget.querySelector('.watertime-water-icon');
        if (iconElement) {
            // 使用简洁的水杯SVG图标
            iconElement.innerHTML = `
                <svg viewBox="0 0 16 16" style="width: 100%; height: 100%;">
                    <!-- 水杯轮廓 -->
                    <path d="M4 2h8c0.5 0 1 0.5 1 1v9c0 1.5-1 2.5-2.5 2.5h-5C4 14.5 3 13.5 3 12V3c0-0.5 0.5-1 1-1z"
                          fill="none" stroke="#00BFFF" stroke-width="1.2" stroke-linejoin="round"/>
                    <!-- 水位 -->
                    <path d="M4.2 6h7.6v6c0 1-0.8 1.8-1.8 1.8h-4c-1 0-1.8-0.8-1.8-1.8V6z"
                          fill="#87CEEB" opacity="0.7"/>
                    <!-- 水面波纹 -->
                    <path d="M4.2 6c0.8 0 1.2 0.5 2 0.5s1.2-0.5 2-0.5s1.2 0.5 2 0.5s1.2-0.5 1.8-0.5"
                          fill="none" stroke="#00BFFF" stroke-width="0.8" opacity="0.8"/>
                    <!-- 杯口椭圆 -->
                    <ellipse cx="8" cy="2.5" rx="4" ry="0.5" fill="none" stroke="#00BFFF" stroke-width="0.8"/>
                </svg>
            `;
        }

        // 绑定事件
        bindWidgetEvents(widget);
        
        // 加载数据并更新显示
        loadWaterTimeData().then(() => {
            updateWaterTimeDisplay();
        });
        
        console.log('WaterTime 浮动圆圈初始化完成');
    }

    // 绑定浮动圆圈事件
    function bindWidgetEvents(widget) {
        // 鼠标事件
        widget.addEventListener('mousedown', handleMouseDown);
        widget.addEventListener('click', handleWaterClick);

        // 触摸事件（移动端支持）
        widget.addEventListener('touchstart', handleTouchStart, { passive: false });

        // 全局事件
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.addEventListener('touchmove', handleTouchMove, { passive: false });
        document.addEventListener('touchend', handleTouchEnd);

        // 防止拖拽时选中文本
        widget.addEventListener('selectstart', (e) => e.preventDefault());
        widget.addEventListener('dragstart', (e) => e.preventDefault());
    }

    // 鼠标按下事件
    function handleMouseDown(e) {
        e.preventDefault();
        startDrag(e.clientX, e.clientY);
    }

    // 触摸开始事件
    function handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        startDrag(touch.clientX, touch.clientY);
    }

    // 开始拖拽
    function startDrag(clientX, clientY) {
        watertimeState.isDragging = true;
        watertimeState.startPosition = { x: clientX, y: clientY };
        watertimeState.dragStartTime = Date.now(); // 记录拖拽开始时间

        const widget = watertimeState.widget;
        const rect = widget.getBoundingClientRect();

        // 计算鼠标相对于圆圈的偏移
        watertimeState.dragOffset = {
            x: clientX - rect.left,
            y: clientY - rect.top
        };

        // 添加拖拽样式
        widget.classList.add('dragging');
        widget.style.cursor = 'grabbing';
        widget.style.userSelect = 'none';

        // 0.5秒后添加长时间拖拽样式
        setTimeout(() => {
            if (watertimeState.isDragging) {
                widget.classList.add('long-drag');
                console.log('拖拽时间超过0.5秒，将不触发点击事件');
            }
        }, 500);

        console.log('开始拖拽');
    }

    // 鼠标移动事件
    function handleMouseMove(e) {
        if (watertimeState.isDragging) {
            e.preventDefault();
            updateDragPosition(e.clientX, e.clientY);
        }
    }

    // 触摸移动事件
    function handleTouchMove(e) {
        if (watertimeState.isDragging) {
            e.preventDefault();
            const touch = e.touches[0];
            updateDragPosition(touch.clientX, touch.clientY);
        }
    }

    // 更新拖拽位置
    function updateDragPosition(clientX, clientY) {
        const widget = watertimeState.widget;

        // 计算新位置
        let newX = clientX - watertimeState.dragOffset.x;
        let newY = clientY - watertimeState.dragOffset.y;

        // 获取窗口尺寸和圆圈尺寸
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const widgetRect = widget.getBoundingClientRect();
        const widgetWidth = widgetRect.width;
        const widgetHeight = widgetRect.height;

        // 边界限制
        newX = Math.max(0, Math.min(newX, windowWidth - widgetWidth));
        newY = Math.max(0, Math.min(newY, windowHeight - widgetHeight));

        // 应用新位置
        widget.style.left = newX + 'px';
        widget.style.top = newY + 'px';
        widget.style.right = 'auto';
        widget.style.bottom = 'auto';

        // 记录位置
        watertimeState.lastPosition = { x: newX, y: newY };
    }

    // 鼠标释放事件
    function handleMouseUp(e) {
        if (watertimeState.isDragging) {
            endDrag(e.clientX, e.clientY);
        }
    }

    // 触摸结束事件
    function handleTouchEnd(e) {
        if (watertimeState.isDragging) {
            const touch = e.changedTouches[0];
            endDrag(touch.clientX, touch.clientY);
        }
    }

    // 结束拖拽
    function endDrag(clientX, clientY) {
        const widget = watertimeState.widget;

        // 计算拖拽时长
        watertimeState.dragDuration = Date.now() - watertimeState.dragStartTime;

        // 计算拖拽距离
        const dragDistance = Math.sqrt(
            Math.pow(clientX - watertimeState.startPosition.x, 2) +
            Math.pow(clientY - watertimeState.startPosition.y, 2)
        );

        // 恢复样式
        widget.classList.remove('dragging', 'long-drag');
        widget.style.cursor = 'grab';
        widget.style.userSelect = '';

        // 保存位置到存储
        saveWidgetPosition();

        // 判断是否应该触发点击事件
        const shouldTriggerClick = dragDistance < 5 && watertimeState.dragDuration < 500; // 距离小于5px且时间小于0.5秒

        watertimeState.isDragging = false;

        console.log('结束拖拽 - 距离:', dragDistance, '时长:', watertimeState.dragDuration, '是否触发点击:', shouldTriggerClick);
        console.log('拖拽结束，位置:', watertimeState.lastPosition);
    }

    // 保存浮动圆圈位置
    async function saveWidgetPosition() {
        try {
            await chrome.storage.local.set({
                widgetPosition: watertimeState.lastPosition
            });
            console.log('浮动圆圈位置已保存:', watertimeState.lastPosition);
        } catch (error) {
            console.error('保存浮动圆圈位置失败:', error);
        }
    }

    // 加载浮动圆圈位置
    async function loadWidgetPosition() {
        try {
            const result = await chrome.storage.local.get(['widgetPosition']);
            if (result.widgetPosition) {
                watertimeState.lastPosition = result.widgetPosition;
                applyWidgetPosition();
                console.log('浮动圆圈位置已加载:', result.widgetPosition);
            }
        } catch (error) {
            console.error('加载浮动圆圈位置失败:', error);
        }
    }

    // 应用浮动圆圈位置
    function applyWidgetPosition() {
        const widget = watertimeState.widget;
        if (widget && watertimeState.lastPosition.x !== undefined) {
            widget.style.left = watertimeState.lastPosition.x + 'px';
            widget.style.top = watertimeState.lastPosition.y + 'px';
            widget.style.right = 'auto';
            widget.style.bottom = 'auto';
        }
    }
    
    // 加载数据
    async function loadWaterTimeData() {
        try {
            // 获取配置和设置
            const configResult = await chrome.storage.local.get([
                'dailyTarget', 'singleDrink', 'floatPosition',
                'circleSize', 'enableAnimations'
            ]);

            // 更新数据配置
            if (configResult.dailyTarget) {
                watertimeData.dailyTarget = configResult.dailyTarget;
            }
            if (configResult.singleDrink) {
                watertimeData.singleDrink = configResult.singleDrink;
            }

            // 更新显示设置
            if (configResult.floatPosition) {
                watertimeSettings.floatPosition = configResult.floatPosition;
            }
            if (configResult.circleSize) {
                watertimeSettings.circleSize = configResult.circleSize;
            }
            if (configResult.enableAnimations !== undefined) {
                watertimeSettings.enableAnimations = configResult.enableAnimations;
            }

            // 获取今日数据
            const today = getTodayKey();
            const todayResult = await chrome.storage.local.get([today]);
            if (todayResult[today]) {
                watertimeData.todayTotal = todayResult[today].total || 0;
                watertimeData.todayRecords = todayResult[today].records || [];
            }

            // 加载浮动圆圈位置
            await loadWidgetPosition();

            // 应用设置到UI
            applySettings();

            console.log('WaterTime 数据加载完成:', watertimeData, watertimeSettings);
        } catch (error) {
            console.error('WaterTime 数据加载失败:', error);
        }
    }

    // 应用设置到UI
    function applySettings() {
        if (!watertimeState.widget) return;

        // 应用位置设置
        applyPositionSetting();

        // 应用大小设置
        applySizeSetting();

        // 应用动画设置
        applyAnimationSetting();
    }

    // 应用位置设置
    function applyPositionSetting() {
        const widget = watertimeState.widget;

        // 重置所有位置样式
        widget.style.top = '';
        widget.style.bottom = '';
        widget.style.left = '';
        widget.style.right = '';

        // 根据设置应用位置
        switch (watertimeSettings.floatPosition) {
            case 'top-left':
                widget.style.top = '20px';
                widget.style.left = '20px';
                break;
            case 'top-right':
                widget.style.top = '20px';
                widget.style.right = '20px';
                break;
            case 'bottom-left':
                widget.style.bottom = '20px';
                widget.style.left = '20px';
                break;
            case 'bottom-right':
            default:
                widget.style.bottom = '20px';
                widget.style.right = '20px';
                break;
        }
    }

    // 应用大小设置
    function applySizeSetting() {
        const widget = watertimeState.widget;
        const progressCircle = widget.querySelector('.watertime-progress-circle');
        const progressContent = widget.querySelector('.watertime-progress-content');

        let size, contentSize, percentageFontSize, amountFontSize;

        switch (watertimeSettings.circleSize) {
            case 'small':
                size = '100px';
                contentSize = '75px';
                percentageFontSize = '16px';
                amountFontSize = '10px';
                break;
            case 'large':
                size = '140px';
                contentSize = '105px';
                percentageFontSize = '20px';
                amountFontSize = '12px';
                break;
            case 'medium':
            default:
                size = '120px';
                contentSize = '90px';
                percentageFontSize = '18px';
                amountFontSize = '11px';
                break;
        }

        widget.style.width = size;
        widget.style.height = size;
        progressCircle.style.width = size;
        progressCircle.style.height = size;
        progressContent.style.width = contentSize;
        progressContent.style.height = contentSize;

        const percentageElement = widget.querySelector('.watertime-percentage');
        if (percentageElement) {
            percentageElement.style.fontSize = percentageFontSize;
        }

        const amountElement = widget.querySelector('.watertime-amount');
        if (amountElement) {
            amountElement.style.fontSize = amountFontSize;
        }
    }

    // 应用动画设置
    function applyAnimationSetting() {
        const widget = watertimeState.widget;

        if (watertimeSettings.enableAnimations) {
            widget.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        } else {
            widget.style.transition = 'none';
        }
    }

    // 更新显示
    function updateWaterTimeDisplay() {
        if (!watertimeState.widget) return;

        const percentage = Math.min(Math.round((watertimeData.todayTotal / watertimeData.dailyTarget) * 100), 100);

        // 更新百分比文字
        watertimeState.percentageElement.textContent = `${percentage}%`;

        // 更新毫升量文字
        if (watertimeState.amountElement) {
            watertimeState.amountElement.textContent = `${watertimeData.todayTotal}ml`;
        }

        // 更新进度圈
        updateProgressCircle(percentage);
    }
    
    // 更新进度圈
    function updateProgressCircle(percentage) {
        if (!watertimeState.progressArc) return;

        const circumference = 2 * Math.PI * 52; // 半径52的圆周长
        const offset = circumference - (percentage / 100) * circumference;

        watertimeState.progressArc.style.strokeDashoffset = offset;
        
        // 根据进度改变颜色和效果
        if (percentage >= 100) {
            watertimeState.progressArc.style.stroke = '#32CD32';
            watertimeState.progressArc.classList.add('glowing');
        } else if (percentage >= 75) {
            watertimeState.progressArc.style.stroke = '#00BFFF';
            watertimeState.progressArc.classList.add('glowing');
        } else if (percentage >= 50) {
            watertimeState.progressArc.style.stroke = '#87CEEB';
            watertimeState.progressArc.classList.remove('glowing');
        } else {
            watertimeState.progressArc.style.stroke = '#B0E0E6';
            watertimeState.progressArc.classList.remove('glowing');
        }
    }
    
    // 处理点击事件
    async function handleWaterClick(event) {
        // 如果正在拖拽，不处理点击
        if (watertimeState.isDragging) {
            return;
        }

        // 如果刚结束拖拽且拖拽时间超过0.5秒，不处理点击
        if (watertimeState.dragDuration > 500) {
            console.log('拖拽时间超过0.5秒，忽略点击事件');
            watertimeState.dragDuration = 0; // 重置拖拽时长
            return;
        }

        event.preventDefault();
        event.stopPropagation();

        // 防抖处理
        const now = Date.now();
        if (watertimeState.isProcessing || (now - watertimeState.lastClickTime) < 500) {
            return;
        }
        
        watertimeState.isProcessing = true;
        watertimeState.lastClickTime = now;

        // 重置拖拽时长，确保不影响后续点击
        watertimeState.dragDuration = 0;

        try {
            console.log(`记录饮水: ${watertimeData.singleDrink}ml`);
            
            // 添加点击动画
            watertimeState.widget.classList.add('clicked');
            setTimeout(() => {
                watertimeState.widget.classList.remove('clicked');
            }, 800);
            
            // 更新数据
            const oldTotal = watertimeData.todayTotal;
            watertimeData.todayTotal += watertimeData.singleDrink;
            watertimeData.todayRecords.push(getCurrentTimeString());
            
            // 保存数据
            await saveWaterTimeData();
            
            // 更新显示
            updateWaterTimeDisplay();
            
            // 成功反馈
            setTimeout(() => {
                watertimeState.widget.classList.add('success');
                setTimeout(() => {
                    watertimeState.widget.classList.remove('success');
                }, 1000);
            }, 400);
            
            // 检查是否达成目标
            if (oldTotal < watertimeData.dailyTarget && watertimeData.todayTotal >= watertimeData.dailyTarget) {
                showCelebration();
            }
            
        } catch (error) {
            console.error('记录饮水失败:', error);
        } finally {
            setTimeout(() => {
                watertimeState.isProcessing = false;
            }, 1000);
        }
    }
    
    // 保存数据
    async function saveWaterTimeData() {
        try {
            const today = getTodayKey();
            const todayData = {
                total: watertimeData.todayTotal,
                records: watertimeData.todayRecords
            };
            
            await chrome.storage.local.set({
                [today]: todayData
            });
            
            console.log('WaterTime 数据已保存');
        } catch (error) {
            console.error('WaterTime 数据保存失败:', error);
        }
    }
    
    // 获取今日键名
    function getTodayKey() {
        const today = new Date();
        return `watertime_${today.getFullYear()}_${today.getMonth() + 1}_${today.getDate()}`;
    }
    
    // 获取当前时间字符串
    function getCurrentTimeString() {
        const now = new Date();
        return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    }
    
    // 显示庆祝动画
    function showCelebration() {
        console.log('🎉 恭喜达成今日饮水目标！');
        
        // 可以在这里添加更多庆祝效果
        if (watertimeState.widget) {
            watertimeState.widget.style.animation = 'watertime-success-pulse 2s ease-out';
            setTimeout(() => {
                watertimeState.widget.style.animation = '';
            }, 2000);
        }
    }
    
    // 监听存储变化
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
            let needUpdate = false;
            let needApplySettings = false;

            // 检查数据配置变化
            if (changes.dailyTarget) {
                watertimeData.dailyTarget = changes.dailyTarget.newValue;
                needUpdate = true;
            }

            if (changes.singleDrink) {
                watertimeData.singleDrink = changes.singleDrink.newValue;
                needUpdate = true;
            }

            // 检查显示设置变化
            if (changes.floatPosition) {
                watertimeSettings.floatPosition = changes.floatPosition.newValue;
                needApplySettings = true;
            }

            if (changes.circleSize) {
                watertimeSettings.circleSize = changes.circleSize.newValue;
                needApplySettings = true;
            }

            if (changes.enableAnimations) {
                watertimeSettings.enableAnimations = changes.enableAnimations.newValue;
                needApplySettings = true;
            }

            // 检查今日数据变化
            const today = getTodayKey();
            if (changes[today]) {
                const newData = changes[today].newValue;
                if (newData) {
                    watertimeData.todayTotal = newData.total || 0;
                    watertimeData.todayRecords = newData.records || [];
                    needUpdate = true;
                }
            }

            // 应用变化
            if (needApplySettings) {
                applySettings();
            }

            if (needUpdate) {
                updateWaterTimeDisplay();
            }
        }
    });
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWaterTimeWidget);
    } else {
        initWaterTimeWidget();
    }
}
