/* WaterTime 浮动圆圈样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* 移除所有系统级别的选择和焦点效果 */
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    outline: none !important;
}

body {
    background: transparent !important;
    overflow: hidden;
    user-select: none;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    /* 使用弹性布局确保内容完全贴合窗口 */
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.float-container {
    width: 80px;   /* 缩小到80px，紧贴实际内容，避免透明边框阻挡点击 */
    height: 80px;  /* 缩小到80px，紧贴实际内容，避免透明边框阻挡点击 */
    position: relative;
    background: transparent !important;
    /* 弹性布局居中显示圆形内容 */
    display: flex;
    align-items: center;
    justify-content: center;
    /* 恢复鼠标事件捕获，允许交互 */
    pointer-events: auto;
    /* 优化性能 */
    will-change: transform;
    transform: translateZ(0);
    contain: layout style paint;
}

.float-widget {
    width: 80px;   /* 缩小到80px，与窗口尺寸完全匹配 */
    height: 80px;  /* 缩小到80px，与窗口尺寸完全匹配 */
    position: relative;
    border-radius: 50%;
    /* 恢复原来的透明样式 */
    background: transparent;
    border: none;
    box-shadow: none;
    /* 允许JavaScript控制过渡效果 */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    overflow: visible; /* 允许右键菜单超出 */
    /* 恢复鼠标事件捕获 */
    pointer-events: auto;
    /* 移除系统效果 */
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-appearance: none !important;
    outline: none !important;
    /* 优化性能 */
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    contain: layout style paint;
}

/* 移除CSS悬停效果，让JavaScript完全控制动画 */
.float-widget:active {
    cursor: grabbing;
}

/* 拖拽状态优化 */
.float-container.dragging .float-widget {
    transition: none !important; /* 拖拽时禁用过渡 */
    transform: scale(0.98) translateZ(0); /* 拖拽时稍微缩小 */
    /* 移除所有背景和边框效果 */
}

/* 拖拽手柄 */
.drag-handle {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;  /* 与浮窗尺寸完全匹配 */
    height: 80px; /* 与浮窗尺寸完全匹配 */
    border-radius: 50%;
    z-index: 10;
    cursor: grab;
    /* 恢复鼠标事件捕获，确保拖拽功能正常 */
    pointer-events: auto;
    /* 优化拖拽性能 */
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    /* 确保拖拽响应 */
    touch-action: none;
    user-select: none;
    -webkit-user-drag: none;
    /* 移除调试背景，保持透明 */
    background: transparent;
}

.drag-handle:active {
    cursor: grabbing;
    transform: translateZ(0);
}

/* 进度容器 */
.progress-container {
    position: relative;
    width: 100px;  /* 与实际进度圆环尺寸匹配 */
    height: 100px; /* 与实际进度圆环尺寸匹配 */
    z-index: 5;
}

.progress-circle {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
    filter: drop-shadow(0 2px 4px rgba(0, 191, 255, 0.3));
}

.progress-bg {
    fill: none;
    stroke: rgba(135, 206, 235, 0.2); /* 更淡的背景圆环 */
    stroke-width: 8; /* 按比例增加：6 * 1.3 ≈ 8 */
    stroke-linecap: round;
}

.progress-arc {
    fill: none;
    stroke: #00BFFF; /* 鲜明的蓝色进度圆环 */
    stroke-width: 8; /* 按比例增加：6 * 1.3 ≈ 8 */
    stroke-linecap: round;
    stroke-dasharray: 424.75; /* 2 * π * 67.6 = 424.75 */
    stroke-dashoffset: 424.75;
    transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 8px rgba(0, 191, 255, 0.6)); /* 按比例增强发光效果 */
}

.progress-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
    z-index: 6;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px; /* 2 * 1.3 ≈ 3 */
}

.water-icon {
    color: #00BFFF;
    opacity: 0.8;
    margin-bottom: 2px;
}

.water-icon svg {
    width: 23px; /* 18 * 1.3 ≈ 23 */
    height: 23px; /* 18 * 1.3 ≈ 23 */
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.percentage {
    font-size: 18px; /* 14 * 1.3 ≈ 18 */
    font-weight: 600;
    color: #00BFFF;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.amount {
    font-size: 13px !important; /* 10 * 1.3 = 13 */
    color: #000000 !important; /* 强制改为黑色 */
    line-height: 1;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important; /* 白色阴影增强可读性 */
}

/* 水波动画 */
.water-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.water-animation.active {
    opacity: 0.6;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, rgba(0, 191, 255, 0.3) 0%, transparent 100%);
    border-radius: 50%;
    transform: translateY(100%);
}

.wave1 {
    animation: wave 3s ease-in-out infinite;
}

.wave2 {
    animation: wave 3s ease-in-out infinite 0.5s;
}

.wave3 {
    animation: wave 3s ease-in-out infinite 1s;
}

@keyframes wave {
    0%, 100% {
        transform: translateY(100%);
    }
    50% {
        transform: translateY(20%);
    }
}

/* 快速操作按钮 - 完全隐藏 */
.quick-actions {
    display: none !important; /* 完全隐藏快速操作按钮 */
}

.float-widget:hover .quick-actions {
    display: none !important; /* 悬停时也不显示 */
}

.quick-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    color: #00BFFF;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-btn:hover {
    transform: scale(1.1);
    background: rgba(0, 191, 255, 0.1);
    box-shadow: 0 6px 16px rgba(0, 191, 255, 0.3);
}

.quick-btn:active {
    transform: scale(0.95);
}

/* 状态指示器 - 完全隐藏 */
.status-indicator {
    display: none !important; /* 完全隐藏状态指示器 */
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #22c55e;
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.5);
    animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.warning {
    background: #fbbf24;
    box-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

.status-dot.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

/* 提示信息 */
.tooltip {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 25;
    pointer-events: none;
}

.tooltip.show {
    opacity: 1;
    visibility: visible;
    bottom: -35px;
}

.tooltip::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(0, 0, 0, 0.8);
}

/* 右键菜单 */
.context-menu {
    position: fixed;  /* 改为fixed，相对于屏幕定位 */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    padding: 4px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    min-width: 140px;
    max-width: 200px;
    white-space: nowrap;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    color: #2F4F4F;
}

.menu-item:hover {
    background: rgba(0, 191, 255, 0.1);
}

.menu-item svg {
    width: 16px;
    height: 16px;
    color: #5F9EA0;
}

.menu-separator {
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    margin: 4px 0;
}

/* 拖拽状态 - 移除所有效果 */
.float-container.dragging .float-widget {
    transform: scale(0.98) translateZ(0); /* 只保留轻微缩小 */
    box-shadow: none !important; /* 完全移除阴影 */
    cursor: grabbing;
}

.float-container.dragging .quick-actions {
    opacity: 0;
    visibility: hidden;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.08);
    }
    100% {
        transform: scale(1);
    }
}

/* 点击动画 */
.float-widget.clicked {
    animation: click-effect 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes click-effect {
    0% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(1.3) rotate(5deg);
    }
    50% {
        transform: scale(1.1) rotate(-3deg);
    }
    75% {
        transform: scale(1.2) rotate(2deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

/* 成功动画 */
.float-widget.success {
    animation: success-pulse 1s ease-out;
}

@keyframes success-pulse {
    0% {
        transform: scale(1);
        filter: drop-shadow(0 0 0 rgba(50, 205, 50, 0.7));
    }
    50% {
        transform: scale(1.15);
        filter: drop-shadow(0 0 20px rgba(50, 205, 50, 0.8));
    }
    100% {
        transform: scale(1);
        filter: drop-shadow(0 0 0 rgba(50, 205, 50, 0));
    }
}

/* 庆祝动画 */
.float-widget.celebration {
    animation: celebration-bounce 2s ease-out;
}

@keyframes celebration-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) scale(1);
    }
    10% {
        transform: translateY(-10px) scale(1.1);
    }
    30% {
        transform: translateY(-5px) scale(1.05);
    }
    60% {
        transform: translateY(-8px) scale(1.08);
    }
    90% {
        transform: translateY(-3px) scale(1.03);
    }
}

.float-widget.pulse {
    animation: none !important; /* 移除脉冲动画 */
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 移除所有伪元素效果 */
.float-widget::after {
    display: none !important;
}

.float-widget.ripple::after {
    display: none !important;
}

/* 响应式调整 - 更新为1.3倍大小 */
@media (max-width: 1366px) {
    .float-container {
        width: 130px; /* 100 * 1.3 = 130 */
        height: 130px; /* 100 * 1.3 = 130 */
    }

    .progress-container {
        width: 104px; /* 80 * 1.3 = 104 */
        height: 104px; /* 80 * 1.3 = 104 */
    }

    .percentage {
        font-size: 18px; /* 14 * 1.3 ≈ 18 */
    }

    .amount {
        font-size: 10px !important; /* 8 * 1.3 ≈ 10 */
        color: #000000 !important; /* 强制黑色 */
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important; /* 白色阴影 */
    }

    .quick-btn {
        width: 36px; /* 28 * 1.3 ≈ 36 */
        height: 36px; /* 28 * 1.3 ≈ 36 */
    }

    .quick-actions {
        gap: 8px; /* 6 * 1.3 ≈ 8 */
    }
}
