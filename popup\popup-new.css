/* WaterTime 详情页面样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    min-height: 500px;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #E0F6FF 0%, #B0E0E6 50%, #87CEEB 100%);
    color: #2F4F4F;
    overflow-x: hidden;
    user-select: none;
}

/* 主容器 */
.container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 10px;
}

.title {
    font-size: 22px;
    font-weight: 600;
    color: #2F4F4F;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    margin-bottom: 5px;
}

.subtitle {
    font-size: 14px;
    color: #5F9EA0;
    opacity: 0.8;
}

/* 今日概览 */
.today-overview {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-card {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.overview-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.overview-label {
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.overview-value {
    text-align: right;
}

.percentage {
    font-size: 24px;
    font-weight: bold;
    color: #00BFFF;
    display: block;
    line-height: 1;
}

.amount {
    font-size: 14px;
    color: #5F9EA0;
    margin-top: 2px;
}

.overview-remaining {
    text-align: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 14px;
    color: #5F9EA0;
}



/* 饮水日历 */
.water-calendar {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.calendar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #2F4F4F;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    border-radius: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #5F9EA0;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-bottom: 8px;
}

.weekday {
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #5F9EA0;
    padding: 8px 4px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-bottom: 16px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.1);
    color: #2F4F4F;
}

.calendar-day:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.calendar-day.other-month {
    color: #B0C4DE;
    opacity: 0.5;
}

.calendar-day.today {
    background: rgba(0, 191, 255, 0.3);
    color: #00BFFF;
    font-weight: 600;
    border: 2px solid #00BFFF;
}

/* 选中的日期样式 */
.calendar-day.selected {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
    transform: scale(1.1);
    z-index: 2;
    position: relative;
    border: 2px solid #FF5252;
}

/* 今天且被选中的样式 */
.calendar-day.today.selected {
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
    color: white;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.5), 0 2px 8px rgba(0, 191, 255, 0.3);
    border: 2px solid #FF5252;
}

/* 可点击的日期悬停效果 */
.calendar-day:not(.other-month):hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day.achieved {
    background: rgba(34, 197, 94, 0.8);
    color: white;
}

.calendar-day.achieved::after {
    content: '✓';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-day.partial {
    background: rgba(251, 191, 36, 0.8);
    color: white;
}

.calendar-day.partial::after {
    content: '◐';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-day.missed {
    background: rgba(239, 68, 68, 0.8);
    color: white;
}

.calendar-day.missed::after {
    content: '✗';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 8px;
    color: white;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #5F9EA0;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-dot.achieved {
    background: rgba(34, 197, 94, 0.8);
}

.legend-dot.partial {
    background: rgba(251, 191, 36, 0.8);
}

.legend-dot.missed {
    background: rgba(239, 68, 68, 0.8);
}

/* 顶部快速操作 */
.top-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

/* 底部操作 */
.bottom-actions {
    display: flex;
    gap: 12px;
}

/* 统一的操作按钮样式 */
.action-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-btn.primary {
    background: rgba(0, 191, 255, 0.2);
    color: #00BFFF;
}

.quick-btn.primary:hover {
    background: rgba(0, 191, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.3);
}

.quick-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #5F9EA0;
}

.quick-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-btn:active {
    transform: translateY(0);
}

.quick-btn svg {
    width: 16px;
    height: 16px;
}

/* 提示信息 */
.tip {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-align: center;
}

.tip p {
    font-size: 13px;
    color: #5F9EA0;
    line-height: 1.4;
}

/* 制作者信息 */
.author-info {
    text-align: center;
    margin-top: 15px;
    padding: 8px 15px;
    background: rgba(0, 191, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(0, 191, 255, 0.2);
    backdrop-filter: blur(10px);
}

.author-info span {
    color: #00BFFF;
    font-size: 12px;
    font-weight: 500;
    opacity: 0.8;
    letter-spacing: 0.5px;
}

/* 滚动条样式 */
.records-list::-webkit-scrollbar {
    width: 4px;
}

.records-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.records-list::-webkit-scrollbar-thumb {
    background: rgba(0, 191, 255, 0.3);
    border-radius: 2px;
}

.records-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 191, 255, 0.5);
}

/* 动画效果 */
.overview-card,
.today-records,
.quick-actions,
.tip,
.author-info {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 设置面板 - 全屏显示 */
.settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #E6F7FF 0%, #B3E5FC 100%);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.settings-panel.show {
    transform: translateX(0);
}

.settings-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid rgba(0, 191, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.settings-header h3 {
    margin: 0;
    color: #00BFFF;
    font-size: 22px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 191, 255, 0.1);
}

.close-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(0, 191, 255, 0.2);
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(0, 191, 255, 0.1);
    color: #00BFFF;
    border-color: #00BFFF;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.2);
}

.settings-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-group {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.setting-group h4 {
    margin: 0 0 20px 0;
    color: #00BFFF;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(0, 191, 255, 0.2);
}

.setting-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.setting-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.setting-item label {
    color: #333;
    font-weight: 600;
    font-size: 16px;
    flex: 1;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.input-group input {
    width: 100px;
    padding: 12px 16px;
    border: 2px solid #E0E0E0;
    border-radius: 8px;
    font-size: 16px;
    text-align: center;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-group input:focus {
    outline: none;
    border-color: #00BFFF;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

.input-group .unit {
    color: #666;
    font-size: 14px;
    font-weight: 600;
}

.setting-item select {
    padding: 12px 16px;
    border: 2px solid #E0E0E0;
    border-radius: 8px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.setting-item select:focus {
    outline: none;
    border-color: #00BFFF;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
}

/* 开关切换 */
.toggle-switch {
    position: relative;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-label {
    display: block;
    width: 60px;
    height: 32px;
    background: #E0E0E0;
    border-radius: 16px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider {
    position: absolute;
    top: 4px;
    left: 4px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label {
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(0, 191, 255, 0.2);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label .toggle-slider {
    transform: translateX(28px);
    box-shadow: 0 2px 8px rgba(0, 191, 255, 0.3);
}

/* 设置操作按钮 */
.settings-actions {
    display: flex;
    gap: 16px;
    margin-top: auto;
    padding: 20px 0 0 0;
    border-top: 2px solid rgba(0, 191, 255, 0.2);
}

.settings-actions .btn {
    flex: 1;
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-actions .btn.primary {
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
    color: white;
}

.settings-actions .btn.primary:hover {
    background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 191, 255, 0.3);
}

.settings-actions .btn.secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    border: 2px solid #E0E0E0;
}

.settings-actions .btn.secondary:hover {
    background: white;
    border-color: #00BFFF;
    color: #00BFFF;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 191, 255, 0.2);
}

/* 响应式 */
@media (max-width: 320px) {
    body {
        width: 300px;
    }

    .container {
        padding: 15px;
        gap: 15px;
    }

    .title {
        font-size: 20px;
    }

    .percentage {
        font-size: 20px;
    }

    .top-actions,
    .bottom-actions {
        flex-direction: column;
    }

    /* 全屏设置页面响应式 */
    .settings-header {
        padding: 16px 20px;
    }

    .settings-header h3 {
        font-size: 20px;
    }

    .settings-content {
        padding: 16px;
        gap: 16px;
    }

    .setting-group {
        padding: 16px;
    }

    .setting-item {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .setting-item label {
        font-size: 14px;
    }

    .input-group input,
    .setting-item select {
        font-size: 14px;
        padding: 10px 14px;
    }

    .settings-actions {
        flex-direction: column;
        gap: 12px;
    }

    .settings-actions .btn {
        padding: 14px 20px;
        font-size: 14px;
    }
}

/* 主要操作按钮样式 */
.action-btn.primary {
    background: linear-gradient(135deg, #00BFFF 0%, #1E90FF 100%);
    color: white;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 191, 255, 0.3);
}

/* 次要操作按钮样式 */
.action-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #5F9EA0;
}

.action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(95, 158, 160, 0.2);
}

/* 危险操作按钮样式 */
.action-btn.danger {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
    color: white;
}

.action-btn.danger:hover {
    background: linear-gradient(135deg, #FF5252 0%, #FF6B6B 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* 按钮内容布局 */
.action-btn span {
    white-space: nowrap;
}

.action-btn svg {
    flex-shrink: 0;
}

/* 顶部按钮特殊样式 */
.top-actions .action-btn {
    font-size: 13px;
    padding: 10px 14px;
}

/* 底部按钮特殊样式 */
.bottom-actions .action-btn {
    font-size: 14px;
    padding: 14px 20px;
}

/* 禁用按钮样式 */
.action-btn.disabled,
.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    filter: grayscale(50%);
}

.action-btn.disabled:hover,
.action-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}
