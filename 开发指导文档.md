# 饮水记录浏览器扩展开发指导文档

## 项目概述

### 产品名称
WaterTime - 饮水记录浏览器扩展

### 产品定位
一个简洁美观的Microsoft Edge浏览器扩展，帮助用户养成良好的饮水习惯，通过可视化进度圈和简单的点击操作记录每日饮水量。

### 核心功能
1. **饮水记录**：点击水杯图标记录饮水
2. **进度可视化**：圆弧进度圈显示完成度
3. **个性化配置**：设置每日目标和单次饮水量
4. **数据持久化**：本地存储饮水记录

## 技术方案

### 开发技术栈
- **前端技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **扩展框架**：Chrome Extension API (Manifest V3)
- **数据存储**：Chrome Storage API
- **图形绘制**：CSS3 + SVG
- **开发工具**：VS Code + Chrome DevTools

### 为什么可以纯前端开发？
1. ✅ 浏览器扩展本身就是前端技术
2. ✅ 数据存储使用浏览器本地存储，无需服务器
3. ✅ 所有UI效果都可以用CSS和JavaScript实现
4. ✅ 不涉及复杂的后端逻辑或数据库操作

## 项目结构规划

```
watertime/
├── manifest.json          # 扩展配置文件
├── popup/                 # 弹窗界面
│   ├── popup.html         # 弹窗HTML结构
│   ├── popup.css          # 弹窗样式
│   └── popup.js           # 弹窗逻辑
├── options/               # 设置页面
│   ├── options.html       # 设置页面HTML
│   ├── options.css        # 设置页面样式
│   └── options.js         # 设置页面逻辑
├── icons/                 # 图标资源
│   ├── icon16.png         # 16x16图标
│   ├── icon48.png         # 48x48图标
│   └── icon128.png        # 128x128图标
└── assets/                # 其他资源
    └── water-cup.svg      # 水杯图标
```

## 详细功能需求

### 1. 主界面设计（popup.html）

#### 界面布局
- **整体尺寸**：320px × 400px
- **主色调**：天青色 (#87CEEB)
- **布局结构**：
  ```
  ┌─────────────────┐
  │   今天饮水      │
  │                 │
  │  ╭─────────╮    │
  │ ╱  75% 1500ml ╲  │
  │╱              ╲ │
  ││   🥤 水杯图标  │ │
  │╲              ╱ │
  │ ╲____________╱  │
  │                 │
  └─────────────────┘
  ```

#### 核心元素
1. **标题文字**："今天饮水"
2. **进度圈**：SVG圆弧，动态显示进度
3. **进度文字**："百分比% 已饮水量ml"
4. **水杯图标**：可点击的SVG图标

### 2. 配置页面设计（options.html）

#### 配置项目
1. **每日目标饮水量**
   - 输入框，默认值：2000ml
   - 范围：500-5000ml
   - 单位：毫升(ml)

2. **单次饮水量**
   - 输入框，默认值：200ml
   - 范围：50-1000ml
   - 单位：毫升(ml)

#### 界面布局
```
┌─────────────────────────┐
│     饮水记录设置        │
│                         │
│ 每日目标饮水量：        │
│ [2000] ml              │
│                         │
│ 单次饮水量：            │
│ [200] ml               │
│                         │
│     [保存设置]          │
└─────────────────────────┘
```

### 3. 数据存储设计

#### 存储结构
```javascript
// 用户配置
{
  "dailyTarget": 2000,    // 每日目标(ml)
  "singleDrink": 200      // 单次饮水量(ml)
}

// 饮水记录
{
  "2024-01-15": {
    "total": 1200,         // 当日总饮水量
    "records": [           // 饮水记录时间戳
      "09:30", "11:45", "14:20", "16:10", "18:30", "20:15"
    ]
  }
}
```

## 开发步骤指南

### 第一阶段：项目初始化（预计1天）

#### 步骤1：创建基础文件结构
1. 创建项目文件夹和子目录
2. 准备图标资源（16px, 48px, 128px）
3. 创建manifest.json配置文件

#### 步骤2：编写manifest.json
- 定义扩展基本信息
- 配置权限和API使用
- 设置弹窗和选项页面路径

### 第二阶段：主界面开发（预计2-3天）

#### 步骤3：创建弹窗HTML结构
- 设计基础布局
- 添加文字和图标元素
- 确保响应式设计

#### 步骤4：实现CSS样式
- 应用天青色主题
- 创建圆弧进度圈效果
- 设置悬停和点击动画

#### 步骤5：编写JavaScript逻辑
- 实现点击记录功能
- 计算和更新进度
- 数据存储和读取

### 第三阶段：配置页面开发（预计1-2天）

#### 步骤6：创建设置页面
- 设计配置表单
- 添加输入验证
- 实现数据保存功能

#### 步骤7：数据管理
- 实现配置读取和写入
- 添加默认值处理
- 确保数据同步

### 第四阶段：功能完善（预计1-2天）

#### 步骤8：进度圈动画
- 实现SVG圆弧绘制
- 添加进度变化动画
- 优化视觉效果

#### 步骤9：数据重置功能
- 实现每日数据重置
- 添加历史记录查看
- 处理跨日期逻辑

### 第五阶段：测试和优化（预计1天）

#### 步骤10：功能测试
- 测试所有交互功能
- 验证数据存储正确性
- 检查边界情况处理

#### 步骤11：性能优化
- 优化加载速度
- 减少内存占用
- 提升用户体验

## 关键技术要点

### 1. 圆弧进度圈实现
- 使用SVG的`<path>`元素绘制圆弧
- 通过JavaScript动态计算路径参数
- CSS动画实现平滑过渡效果

### 2. 数据存储策略
- 使用`chrome.storage.local`存储用户数据
- 按日期分组存储饮水记录
- 实现数据的增量更新

### 3. 时间处理逻辑
- 获取当前日期作为存储键
- 处理跨日期的数据重置
- 记录具体的饮水时间点

### 4. 用户体验优化
- 点击反馈动画效果
- 进度变化的视觉提示
- 简洁直观的界面设计

## 预期开发时间

- **总开发时间**：7-10天
- **每日开发时间**：2-4小时
- **技能要求**：HTML/CSS/JavaScript基础

## 成功标准

### 功能完整性
- ✅ 能够正确记录饮水量
- ✅ 进度圈准确显示完成度
- ✅ 配置功能正常工作
- ✅ 数据持久化保存

### 用户体验
- ✅ 界面美观符合设计要求
- ✅ 操作流畅无卡顿
- ✅ 反馈及时清晰
- ✅ 配置简单易懂

### 技术质量
- ✅ 代码结构清晰
- ✅ 无明显bug
- ✅ 性能表现良好
- ✅ 兼容Edge浏览器

## 下一步行动

1. **立即开始**：创建项目文件结构
2. **学习资源**：查阅Chrome Extension开发文档
3. **开发工具**：安装VS Code和相关插件
4. **测试环境**：在Edge浏览器中启用开发者模式

---

*本文档将随着开发进度持续更新和完善*
