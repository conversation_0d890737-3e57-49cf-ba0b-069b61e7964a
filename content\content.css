/* WaterTime 浮动圆圈样式 */

/* 浮动容器 */
#watertime-float-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999999;
    width: 120px;
    height: 120px;
    cursor: grab;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    touch-action: none; /* 防止触摸滚动 */
}

/* 悬停时稍微放大 */
#watertime-float-widget:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 8px 20px rgba(0, 191, 255, 0.4));
}

/* 拖拽时的样式 */
#watertime-float-widget:active {
    cursor: grabbing;
    transform: scale(1.02);
}

/* 拖拽状态下禁用悬停效果 */
#watertime-float-widget.dragging {
    transition: none !important;
    cursor: grabbing !important;
    opacity: 0.8;
}

#watertime-float-widget.dragging:hover {
    transform: none !important;
    filter: none !important;
}

/* 长时间拖拽状态 (>0.5秒) */
#watertime-float-widget.long-drag {
    opacity: 0.6;
    filter: drop-shadow(0 4px 12px rgba(255, 165, 0, 0.5));
}

/* 进度圈容器 */
.watertime-progress-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* SVG进度圈 */
.watertime-progress-circle {
    width: 120px;
    height: 120px;
    transform: rotate(-90deg);
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

/* 背景圆圈 */
.watertime-progress-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 6;
}

/* 进度圆弧 */
.watertime-progress-arc {
    fill: none;
    stroke: #00BFFF;
    stroke-width: 6;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.6s ease-in-out, stroke 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 191, 255, 0.3));
}

/* 进度圈内容 */
.watertime-progress-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 90px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.5);
    padding: 8px;
}

/* 百分比文字 */
.watertime-percentage {
    font-size: 18px;
    font-weight: bold;
    color: #00BFFF;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 2px;
}

/* 毫升量文字 */
.watertime-amount {
    font-size: 11px;
    font-weight: 500;
    color: #5F9EA0;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;
}

/* 水杯图标 */
.watertime-water-icon {
    width: 20px;
    height: 20px;
    opacity: 0.8;
    transition: all 0.3s ease;
    border-radius: 2px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 悬停时图标效果 */
#watertime-float-widget:hover .watertime-water-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* 点击动画 */
#watertime-float-widget.clicked {
    animation: watertime-click-effect 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes watertime-click-effect {
    0% { 
        transform: scale(1) rotate(0deg);
    }
    25% { 
        transform: scale(1.3) rotate(5deg);
    }
    50% { 
        transform: scale(1.1) rotate(-3deg);
    }
    75% { 
        transform: scale(1.2) rotate(2deg);
    }
    100% { 
        transform: scale(1) rotate(0deg);
    }
}

/* 成功动画 */
#watertime-float-widget.success {
    animation: watertime-success-pulse 1s ease-out;
}

@keyframes watertime-success-pulse {
    0% { 
        transform: scale(1);
        filter: drop-shadow(0 0 0 rgba(50, 205, 50, 0.7));
    }
    50% { 
        transform: scale(1.15);
        filter: drop-shadow(0 0 20px rgba(50, 205, 50, 0.8));
    }
    100% { 
        transform: scale(1);
        filter: drop-shadow(0 0 0 rgba(50, 205, 50, 0));
    }
}

/* 进度发光效果 */
.watertime-progress-arc.glowing {
    animation: watertime-progress-glow 2s ease-in-out infinite alternate;
}

@keyframes watertime-progress-glow {
    0% { 
        filter: drop-shadow(0 0 4px rgba(0, 191, 255, 0.3));
    }
    100% { 
        filter: drop-shadow(0 0 8px rgba(0, 191, 255, 0.6));
    }
}

/* 隐藏状态 */
#watertime-float-widget.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.8) translateY(20px);
}

/* 响应式适配 */
@media (max-width: 768px) {
    #watertime-float-widget {
        width: 100px;
        height: 100px;
        bottom: 15px;
        right: 15px;
    }

    .watertime-progress-circle {
        width: 100px;
        height: 100px;
    }

    .watertime-progress-content {
        width: 75px;
        height: 75px;
    }

    .watertime-percentage {
        font-size: 16px;
    }

    .watertime-amount {
        font-size: 10px;
    }

    .watertime-water-icon {
        width: 18px;
        height: 18px;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .watertime-progress-arc {
        stroke: #0066CC;
    }
    
    .watertime-percentage {
        color: #0066CC;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    #watertime-float-widget,
    .watertime-progress-arc,
    .watertime-water-icon {
        transition: none !important;
        animation: none !important;
    }
}
