const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的 API
contextBridge.exposeInMainWorld('electronAPI', {
  // ==================== 数据存储相关 ====================
  getStoreData: (key) => ipcRenderer.invoke('get-store-data', key),
  setStoreData: (key, value) => ipcRenderer.invoke('set-store-data', key, value),

  // ==================== 应用信息相关 ====================
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),

  // ==================== 窗口控制相关 ====================
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  showMainWindow: () => ipcRenderer.invoke('show-main-window'),

  // ==================== 浮动窗口相关 ====================
  updateFloatPosition: (x, y) => ipcRenderer.invoke('update-float-position', x, y),
  toggleFloatWindow: () => ipcRenderer.invoke('toggle-float-window'),

  // ==================== 系统功能相关 ====================
  setAutoStartup: (enabled) => ipcRenderer.invoke('set-auto-startup', enabled),
  getAutoStartup: () => ipcRenderer.invoke('get-auto-startup'),

  // ==================== 通知相关 ====================
  showNotification: (title, content, options) => ipcRenderer.invoke('show-notification', title, content, options),

  // ==================== 快速操作 ====================
  quickDrink: (amount) => ipcRenderer.invoke('quick-drink', amount),

  // ==================== 事件监听 ====================
  onDataUpdate: (callback) => {
    const wrappedCallback = (event, data) => callback(data);
    ipcRenderer.on('data-update', wrappedCallback);
    return () => ipcRenderer.removeListener('data-update', wrappedCallback);
  },

  onQuickDrink: (callback) => {
    const wrappedCallback = (event, data) => callback(data);
    ipcRenderer.on('quick-drink', wrappedCallback);
    return () => ipcRenderer.removeListener('quick-drink', wrappedCallback);
  },

  // 移除事件监听
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // ==================== 数据同步相关 ====================
  syncDataToFloat: () => ipcRenderer.invoke('sync-data-to-float'),

  // 监听强制数据刷新消息
  onForceDataRefresh: (callback) => {
    const wrappedCallback = () => callback();
    ipcRenderer.on('force-data-refresh', wrappedCallback);
    return () => ipcRenderer.removeListener('force-data-refresh', wrappedCallback);
  },

  // ==================== 调试和日志 ====================
  log: (message, level = 'info') => {
    console.log(`[Renderer] [${level.toUpperCase()}] ${message}`);
  }
});

// 开发环境下的调试工具
if (process.argv.includes('--dev')) {
  contextBridge.exposeInMainWorld('devTools', {
    openDevTools: () => ipcRenderer.send('open-dev-tools'),
    log: (...args) => console.log('[Preload]', ...args),
    getProcessInfo: () => ({
      platform: process.platform,
      arch: process.arch,
      version: process.version,
      versions: process.versions
    })
  });
}

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('[Renderer Error]', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('[Renderer Unhandled Rejection]', event.reason);
});
