<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A1,1 0 0,1 13,3V4.3C15.8,5.8 17.5,8.7 17.5,12A5.5,5.5 0 0,1 12,17.5A5.5,5.5 0 0,1 6.5,12C6.5,8.7 8.2,5.8 11,4.3V3A1,1 0 0,1 12,2M12,6A4,4 0 0,0 8,10C8,12.4 9.5,14.5 11.7,15.3C11.9,15.4 12.1,15.4 12.3,15.3C14.5,14.5 16,12.4 16,10A4,4 0 0,0 12,6Z" fill="#00BFFF"/>
                    </svg>
                    <h1>WaterTime 设置</h1>
                </div>
                <p class="subtitle">配置您的饮水习惯和目标</p>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 基础设置 -->
            <section class="settings-section">
                <h2 class="section-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                    </svg>
                    基础设置
                </h2>

                <div class="settings-grid">
                    <!-- 每日目标 -->
                    <div class="setting-item">
                        <label for="dailyTarget" class="setting-label">
                            <span class="label-text">每日饮水目标</span>
                            <span class="label-desc">建议成人每日饮水1500-3000ml</span>
                        </label>
                        <div class="input-group">
                            <input type="number" id="dailyTarget" class="setting-input"
                                   min="500" max="10000" step="100" value="2000">
                            <span class="input-unit">ml</span>
                        </div>
                        <div class="input-error" id="dailyTargetError"></div>
                    </div>

                    <!-- 单次饮水量 -->
                    <div class="setting-item">
                        <label for="singleDrink" class="setting-label">
                            <span class="label-text">单次饮水量</span>
                            <span class="label-desc">每次点击记录的饮水量</span>
                        </label>
                        <div class="input-group">
                            <input type="number" id="singleDrink" class="setting-input"
                                   min="50" max="2000" step="50" value="200">
                            <span class="input-unit">ml</span>
                        </div>
                        <div class="input-error" id="singleDrinkError"></div>
                    </div>
                </div>
            </section>

            <!-- 显示设置 -->
            <section class="settings-section">
                <h2 class="section-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                    </svg>
                    显示设置
                </h2>

                <div class="settings-grid">
                    <!-- 浮动圆圈位置 -->
                    <div class="setting-item">
                        <label for="floatPosition" class="setting-label">
                            <span class="label-text">浮动圆圈位置</span>
                            <span class="label-desc">选择圆圈在页面上的显示位置</span>
                        </label>
                        <select id="floatPosition" class="setting-select">
                            <option value="bottom-right">右下角</option>
                            <option value="bottom-left">左下角</option>
                            <option value="top-right">右上角</option>
                            <option value="top-left">左上角</option>
                        </select>
                    </div>

                    <!-- 圆圈大小 -->
                    <div class="setting-item">
                        <label for="circleSize" class="setting-label">
                            <span class="label-text">圆圈大小</span>
                            <span class="label-desc">调整浮动圆圈的显示大小</span>
                        </label>
                        <select id="circleSize" class="setting-select">
                            <option value="small">小 (100px)</option>
                            <option value="medium">中 (120px)</option>
                            <option value="large">大 (140px)</option>
                        </select>
                    </div>

                    <!-- 启用动画 -->
                    <div class="setting-item">
                        <label class="setting-label">
                            <span class="label-text">启用动画效果</span>
                            <span class="label-desc">开启或关闭圆圈的动画效果</span>
                        </label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="enableAnimations" class="toggle-input">
                            <label for="enableAnimations" class="toggle-label">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 数据管理 -->
            <section class="settings-section">
                <h2 class="section-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z"/>
                    </svg>
                    数据管理
                </h2>

                <div class="data-actions">
                    <button id="exportData" class="action-btn secondary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        导出数据
                    </button>
                    <button id="importData" class="action-btn secondary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        导入数据
                    </button>
                    <button id="resetData" class="action-btn danger">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                        </svg>
                        重置数据
                    </button>
                </div>
            </section>
        </main>

        <!-- 底部操作 -->
        <footer class="footer">
            <div class="footer-actions">
                <button id="resetSettings" class="btn secondary">重置设置</button>
                <div class="primary-actions">
                    <button id="cancelSettings" class="btn secondary">取消</button>
                    <button id="saveSettings" class="btn primary">保存设置</button>
                </div>
            </div>
            <div class="save-status" id="saveStatus"></div>
        </footer>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="fileInput" accept=".json" style="display: none;">

    <script src="options.js"></script>
</body>
</html>
