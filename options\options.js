// WaterTime 设置页面脚本

// DOM元素引用
const dailyTargetInput = document.getElementById('dailyTarget');
const singleDrinkInput = document.getElementById('singleDrink');
const floatPositionSelect = document.getElementById('floatPosition');
const circleSizeSelect = document.getElementById('circleSize');
const enableAnimationsToggle = document.getElementById('enableAnimations');

const dailyTargetError = document.getElementById('dailyTargetError');
const singleDrinkError = document.getElementById('singleDrinkError');

const saveSettingsBtn = document.getElementById('saveSettings');
const cancelSettingsBtn = document.getElementById('cancelSettings');
const resetSettingsBtn = document.getElementById('resetSettings');

const exportDataBtn = document.getElementById('exportData');
const importDataBtn = document.getElementById('importData');
const resetDataBtn = document.getElementById('resetData');
const fileInput = document.getElementById('fileInput');

const saveStatus = document.getElementById('saveStatus');

// 默认设置
const defaultSettings = {
    dailyTarget: 2000,
    singleDrink: 200,
    floatPosition: 'bottom-right',
    circleSize: 'medium',
    enableAnimations: true
};

// 当前设置
let currentSettings = { ...defaultSettings };

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('WaterTime 设置页面初始化...');
    
    await loadSettings();
    updateUI();
    bindEvents();
    
    console.log('WaterTime 设置页面初始化完成');
});

// 加载设置
async function loadSettings() {
    try {
        const result = await chrome.storage.local.get([
            'dailyTarget', 'singleDrink', 'floatPosition', 
            'circleSize', 'enableAnimations'
        ]);
        
        // 合并默认设置和保存的设置
        currentSettings = { ...defaultSettings, ...result };
        
        console.log('设置加载完成:', currentSettings);
    } catch (error) {
        console.error('设置加载失败:', error);
        showStatus('设置加载失败', 'error');
    }
}

// 更新UI
function updateUI() {
    dailyTargetInput.value = currentSettings.dailyTarget;
    singleDrinkInput.value = currentSettings.singleDrink;
    floatPositionSelect.value = currentSettings.floatPosition;
    circleSizeSelect.value = currentSettings.circleSize;
    enableAnimationsToggle.checked = currentSettings.enableAnimations;
}

// 绑定事件
function bindEvents() {
    // 输入验证
    dailyTargetInput.addEventListener('input', () => validateDailyTarget());
    dailyTargetInput.addEventListener('blur', () => validateDailyTarget());
    
    singleDrinkInput.addEventListener('input', () => validateSingleDrink());
    singleDrinkInput.addEventListener('blur', () => validateSingleDrink());
    
    // 按钮事件
    saveSettingsBtn.addEventListener('click', handleSaveSettings);
    cancelSettingsBtn.addEventListener('click', handleCancelSettings);
    resetSettingsBtn.addEventListener('click', handleResetSettings);
    
    // 数据管理
    exportDataBtn.addEventListener('click', handleExportData);
    importDataBtn.addEventListener('click', handleImportData);
    resetDataBtn.addEventListener('click', handleResetData);
    fileInput.addEventListener('change', handleFileSelect);
}

// 验证每日目标
function validateDailyTarget() {
    const value = parseInt(dailyTargetInput.value);
    const min = parseInt(dailyTargetInput.min);
    const max = parseInt(dailyTargetInput.max);
    
    clearError(dailyTargetError);
    
    if (isNaN(value)) {
        showError(dailyTargetError, '请输入有效的数字');
        return false;
    }
    
    if (value < min) {
        showError(dailyTargetError, `每日目标不能少于 ${min}ml`);
        return false;
    }
    
    if (value > max) {
        showError(dailyTargetError, `每日目标不能超过 ${max}ml`);
        return false;
    }
    
    if (value % 100 !== 0) {
        showError(dailyTargetError, '请输入100的倍数');
        return false;
    }
    
    return true;
}

// 验证单次饮水量
function validateSingleDrink() {
    const value = parseInt(singleDrinkInput.value);
    const min = parseInt(singleDrinkInput.min);
    const max = parseInt(singleDrinkInput.max);
    
    clearError(singleDrinkError);
    
    if (isNaN(value)) {
        showError(singleDrinkError, '请输入有效的数字');
        return false;
    }
    
    if (value < min) {
        showError(singleDrinkError, `单次饮水量不能少于 ${min}ml`);
        return false;
    }
    
    if (value > max) {
        showError(singleDrinkError, `单次饮水量不能超过 ${max}ml`);
        return false;
    }
    
    if (value % 50 !== 0) {
        showError(singleDrinkError, '请输入50的倍数');
        return false;
    }
    
    return true;
}

// 显示错误
function showError(errorElement, message) {
    errorElement.textContent = message;
    errorElement.classList.add('show');
}

// 清除错误
function clearError(errorElement) {
    errorElement.textContent = '';
    errorElement.classList.remove('show');
}

// 验证所有输入
function validateAllInputs() {
    const isDailyTargetValid = validateDailyTarget();
    const isSingleDrinkValid = validateSingleDrink();
    
    return isDailyTargetValid && isSingleDrinkValid;
}

// 处理保存设置
async function handleSaveSettings() {
    try {
        // 验证输入
        if (!validateAllInputs()) {
            showStatus('请修正输入错误', 'error');
            return;
        }
        
        // 收集设置数据
        const newSettings = {
            dailyTarget: parseInt(dailyTargetInput.value),
            singleDrink: parseInt(singleDrinkInput.value),
            floatPosition: floatPositionSelect.value,
            circleSize: circleSizeSelect.value,
            enableAnimations: enableAnimationsToggle.checked
        };
        
        // 保存设置
        await chrome.storage.local.set(newSettings);
        
        // 更新当前设置
        currentSettings = { ...newSettings };
        
        console.log('设置保存成功:', newSettings);
        showStatus('设置保存成功！', 'success');
        
        // 3秒后隐藏状态
        setTimeout(() => {
            hideStatus();
        }, 3000);
        
    } catch (error) {
        console.error('设置保存失败:', error);
        showStatus('设置保存失败', 'error');
    }
}

// 处理取消设置
function handleCancelSettings() {
    // 恢复到当前设置
    updateUI();
    clearAllErrors();
    showStatus('已取消更改', 'success');
    
    setTimeout(() => {
        hideStatus();
    }, 2000);
}

// 处理重置设置
async function handleResetSettings() {
    if (!confirm('确定要重置所有设置到默认值吗？')) {
        return;
    }
    
    try {
        // 重置到默认设置
        await chrome.storage.local.set(defaultSettings);
        currentSettings = { ...defaultSettings };
        
        // 更新UI
        updateUI();
        clearAllErrors();
        
        console.log('设置重置成功');
        showStatus('设置已重置到默认值', 'success');
        
        setTimeout(() => {
            hideStatus();
        }, 3000);
        
    } catch (error) {
        console.error('设置重置失败:', error);
        showStatus('设置重置失败', 'error');
    }
}

// 处理导出数据
async function handleExportData() {
    try {
        // 获取所有数据
        const allData = await chrome.storage.local.get(null);
        
        // 创建导出对象
        const exportData = {
            version: '1.0.0',
            exportTime: new Date().toISOString(),
            settings: {
                dailyTarget: allData.dailyTarget || defaultSettings.dailyTarget,
                singleDrink: allData.singleDrink || defaultSettings.singleDrink,
                floatPosition: allData.floatPosition || defaultSettings.floatPosition,
                circleSize: allData.circleSize || defaultSettings.circleSize,
                enableAnimations: allData.enableAnimations !== undefined ? allData.enableAnimations : defaultSettings.enableAnimations
            },
            records: {}
        };
        
        // 提取饮水记录
        Object.keys(allData).forEach(key => {
            if (key.startsWith('watertime_')) {
                exportData.records[key] = allData[key];
            }
        });
        
        // 创建下载
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `watertime-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        showStatus('数据导出成功！', 'success');
        
    } catch (error) {
        console.error('数据导出失败:', error);
        showStatus('数据导出失败', 'error');
    }
}

// 处理导入数据
function handleImportData() {
    fileInput.click();
}

// 处理文件选择
async function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
        const text = await file.text();
        const importData = JSON.parse(text);
        
        // 验证数据格式
        if (!importData.version || !importData.settings) {
            throw new Error('无效的备份文件格式');
        }
        
        if (!confirm('确定要导入数据吗？这将覆盖当前的所有设置和记录。')) {
            return;
        }
        
        // 导入设置
        await chrome.storage.local.set(importData.settings);
        
        // 导入记录
        if (importData.records) {
            await chrome.storage.local.set(importData.records);
        }
        
        // 更新当前设置和UI
        currentSettings = { ...importData.settings };
        updateUI();
        
        showStatus('数据导入成功！', 'success');
        
    } catch (error) {
        console.error('数据导入失败:', error);
        showStatus('数据导入失败：' + error.message, 'error');
    } finally {
        // 清空文件输入
        fileInput.value = '';
    }
}

// 处理重置数据
async function handleResetData() {
    if (!confirm('确定要删除所有饮水记录吗？此操作不可恢复！')) {
        return;
    }
    
    try {
        // 获取所有数据
        const allData = await chrome.storage.local.get(null);
        
        // 找出所有记录键
        const recordKeys = Object.keys(allData).filter(key => key.startsWith('watertime_'));
        
        // 删除记录
        await chrome.storage.local.remove(recordKeys);
        
        console.log('数据重置成功，删除了', recordKeys.length, '条记录');
        showStatus(`数据重置成功，删除了 ${recordKeys.length} 条记录`, 'success');
        
    } catch (error) {
        console.error('数据重置失败:', error);
        showStatus('数据重置失败', 'error');
    }
}

// 显示状态
function showStatus(message, type) {
    saveStatus.textContent = message;
    saveStatus.className = `save-status show ${type}`;
}

// 隐藏状态
function hideStatus() {
    saveStatus.classList.remove('show');
}

// 清除所有错误
function clearAllErrors() {
    clearError(dailyTargetError);
    clearError(singleDrinkError);
}
