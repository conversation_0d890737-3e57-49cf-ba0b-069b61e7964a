<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaterTime - 饮水详情</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 标题区域 -->
        <div class="header">
            <h1 class="title">今天饮水</h1>
        </div>

        <!-- 进度圈容器 -->
        <div class="progress-container">
            <!-- SVG进度圈 -->
            <svg class="progress-circle" width="200" height="200" viewBox="0 0 200 200">
                <!-- 背景圆弧 -->
                <circle
                    cx="100"
                    cy="100"
                    r="80"
                    fill="none"
                    stroke="#E0F6FF"
                    stroke-width="8"
                    stroke-linecap="round">
                </circle>

                <!-- 进度圆弧 -->
                <circle
                    cx="100"
                    cy="100"
                    r="80"
                    fill="none"
                    stroke="#00BFFF"
                    stroke-width="8"
                    stroke-linecap="round"
                    stroke-dasharray="502.65"
                    stroke-dashoffset="502.65"
                    transform="rotate(-90 100 100)"
                    class="progress-arc">
                </circle>
            </svg>

            <!-- 进度圈内容 -->
            <div class="progress-content">
                <!-- 进度文字 -->
                <div class="progress-text">
                    <span class="percentage">0%</span>
                    <span class="amount">0ml</span>
                </div>

                <!-- 水杯图标按钮 -->
                <button class="water-button" id="waterButton" title="点击记录饮水">
                    <svg class="water-icon" width="48" height="48" viewBox="0 0 64 64">
                        <!-- 水杯主体 -->
                        <path d="M16 20 L48 20 L46 56 L18 56 Z"
                              fill="#87CEEB"
                              stroke="#5F9EA0"
                              stroke-width="2"/>

                        <!-- 水杯口 -->
                        <ellipse cx="32" cy="20" rx="16" ry="3"
                                 fill="#B0E0E6"
                                 stroke="#5F9EA0"
                                 stroke-width="2"/>

                        <!-- 水面 -->
                        <ellipse cx="31" cy="35" rx="12" ry="2"
                                 fill="#00BFFF"
                                 opacity="0.8"
                                 class="water-surface"/>

                        <!-- 水杯把手 -->
                        <path d="M48 28 Q54 32 48 36"
                              fill="none"
                              stroke="#5F9EA0"
                              stroke-width="3"
                              stroke-linecap="round"/>

                        <!-- 水滴装饰 -->
                        <circle cx="32" cy="42" r="2" fill="#00BFFF" opacity="0.6" class="water-drop"/>
                        <circle cx="28" cy="38" r="1.5" fill="#00BFFF" opacity="0.4" class="water-drop"/>
                        <circle cx="36" cy="40" r="1" fill="#00BFFF" opacity="0.5" class="water-drop"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 底部信息区域 -->
        <div class="footer">
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-label">目标</span>
                    <span class="stat-value" id="targetAmount">2000ml</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已喝</span>
                    <span class="stat-value" id="currentAmount">0ml</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">剩余</span>
                    <span class="stat-value" id="remainingAmount">2000ml</span>
                </div>
            </div>

            <!-- 设置按钮 -->
            <button class="settings-button" id="settingsButton" title="打开设置">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="m12 1 0 6m0 6 0 6"></path>
                    <path d="m17 5-3.5 3.5m-3 3L7 15"></path>
                    <path d="m7 5 3.5 3.5m3 3L17 15"></path>
                </svg>
                设置
            </button>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="popup.js"></script>
</body>
</html>
