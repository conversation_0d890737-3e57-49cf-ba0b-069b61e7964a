// 数据同步专项测试脚本
console.log('🔍 === 数据同步专项测试 ===');

// 测试全局DataManager实例
function testGlobalDataManager() {
    console.log('1. 测试全局DataManager实例:');
    
    console.log('window.dataManager:', window.dataManager ? '✅ 存在' : '❌ 不存在');
    
    if (window.app && window.app.dataManager) {
        console.log('主窗口DataManager:', window.app.dataManager === window.dataManager ? '✅ 使用全局实例' : '❌ 使用独立实例');
    }
    
    if (window.floatWidget && window.floatWidget.dataManager) {
        console.log('浮窗DataManager:', window.floatWidget.dataManager === window.dataManager ? '✅ 使用全局实例' : '❌ 使用独立实例');
    }
}

// 测试监听器机制
function testListeners() {
    console.log('2. 测试监听器机制:');
    
    if (window.dataManager) {
        const listeners = window.dataManager.listeners;
        console.log('监听器数量:', listeners.size);
        
        for (const [key, callbacks] of listeners) {
            console.log(`监听器 "${key}":`, callbacks.length, '个回调');
        }
    }
}

// 测试数据读取
async function testDataRead() {
    console.log('3. 测试数据读取:');
    
    if (window.dataManager) {
        try {
            const settings = await window.dataManager.getSettings();
            const todayData = await window.dataManager.getTodayData();
            
            console.log('设置数据:', settings);
            console.log('今日数据:', todayData);
            
            // 检查数据一致性
            const mainTotal = window.app?.data?.todayTotal || 0;
            const floatTotal = window.floatWidget?.data?.todayTotal || 0;
            const storageTotal = todayData.total || 0;
            
            console.log('数据一致性检查:');
            console.log('- 主窗口:', mainTotal, 'ml');
            console.log('- 浮窗:', floatTotal, 'ml');
            console.log('- 存储:', storageTotal, 'ml');
            
            if (mainTotal === floatTotal && floatTotal === storageTotal) {
                console.log('✅ 数据一致');
            } else {
                console.log('❌ 数据不一致');
            }
            
        } catch (error) {
            console.error('数据读取失败:', error);
        }
    }
}

// 测试数据写入和同步
async function testDataWrite() {
    console.log('4. 测试数据写入和同步:');
    
    if (window.dataManager) {
        try {
            console.log('添加测试记录前的数据:');
            await testDataRead();
            
            // 添加一个测试记录
            console.log('添加200ml测试记录...');
            const record = await window.dataManager.addDrinkRecord(200);
            console.log('记录添加成功:', record);
            
            // 等待同步
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            console.log('添加测试记录后的数据:');
            await testDataRead();
            
        } catch (error) {
            console.error('数据写入测试失败:', error);
        }
    }
}

// 测试监听器触发
function testListenerTrigger() {
    console.log('5. 测试监听器触发:');
    
    if (window.dataManager) {
        // 添加测试监听器
        const testCallback = (data) => {
            console.log('🔔 测试监听器被触发:', data);
        };
        
        window.dataManager.addListener('todayData', testCallback);
        console.log('已添加测试监听器');
        
        // 5秒后移除
        setTimeout(() => {
            window.dataManager.removeListener('todayData', testCallback);
            console.log('已移除测试监听器');
        }, 5000);
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🧪 开始数据同步专项测试...');
    
    testGlobalDataManager();
    testListeners();
    await testDataRead();
    testListenerTrigger();
    
    console.log('📝 如需测试数据写入，请运行: testDataWrite()');
    console.log('🎯 测试完成！');
}

// 导出测试函数
window.dataSyncTest = {
    testGlobalDataManager,
    testListeners,
    testDataRead,
    testDataWrite,
    testListenerTrigger,
    runAllTests
};

console.log('数据同步测试脚本加载完成！');
console.log('运行 dataSyncTest.runAllTests() 开始测试');
